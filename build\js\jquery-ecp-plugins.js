jQuery((function(e){"placeholder"in document.createElement("input")||e("[placeholder]").on({focus(){const t=e(this);t.val()==t.attr("placeholder")&&(t.val(""),t.removeClass("placeholder"))},blur(){const t=e(this);""!=t.val()&&t.val()!=t.attr("placeholder")||(t.addClass("placeholder"),t.val(t.attr("placeholder")))}}).trigger("blur").parents("form").on("submit",(function(){e(this).find("[placeholder]").each((function(){const t=e(this);t.val()==t.attr("placeholder")&&t.val("")}))}))})),window.tec=window.tec||{},window.tec.events=window.tec.events||{},window.tec.events.jqueryEcpPlugins={};