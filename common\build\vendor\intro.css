@keyframes introjspulse{0%{opacity:0;transform:scale(0)}25%{opacity:.1;transform:scale(0)}50%{opacity:.3;transform:scale(.1)}75%{opacity:.5;transform:scale(.5)}to{opacity:0;transform:scale(1)}}.introjs-overlay{box-sizing:initial;opacity:0;position:absolute;transition:all .3s ease-out;z-index:999999}.introjs-showElement{z-index:9999999!important}tr.introjs-showElement>td,tr.introjs-showElement>th{position:relative;z-index:9999999!important}.introjs-disableInteraction{background-color:#fff;opacity:0;position:absolute;z-index:99999999!important}.introjs-relativePosition{position:relative}.introjs-helperLayer{border-radius:4px;box-sizing:initial;position:absolute;transition:all .3s ease-out;z-index:9999998}.introjs-helperLayer *,.introjs-helperLayer :after,.introjs-helperLayer :before{box-sizing:initial}.introjs-tooltipReferenceLayer{background-color:initial;box-sizing:initial;position:absolute;transition:all .3s ease-out;visibility:hidden;z-index:100000000}.introjs-helperNumberLayer,.introjs-tooltipReferenceLayer,.introjs-tooltipReferenceLayer *{font-family:Helvetica Neue,Inter,ui-sans-serif,Apple Color Emoji,Helvetica,Arial,sans-serif}.introjs-helperNumberLayer{color:#9e9e9e;padding-bottom:10px;padding-top:10px;text-align:center}.introjs-arrow{border:5px solid #0000;content:"";position:absolute}.introjs-arrow.top{border-bottom-color:#fff;left:10px;top:-10px}.introjs-arrow.top-right{border-bottom-color:#fff;right:10px;top:-10px}.introjs-arrow.top-middle{border-bottom-color:#fff;left:50%;margin-left:-5px;top:-10px}.introjs-arrow.right{border-left-color:#fff;right:-10px;top:10px}.introjs-arrow.right-bottom{border-left-color:#fff;bottom:10px;right:-10px}.introjs-arrow.bottom{border-top-color:#fff;bottom:-10px;left:10px}.introjs-arrow.bottom-right{border-top-color:#fff;bottom:-10px;right:10px}.introjs-arrow.bottom-middle{border-top-color:#fff;bottom:-10px;left:50%;margin-left:-5px}.introjs-arrow.left{border-right-color:#fff;left:-10px;top:10px}.introjs-arrow.left-bottom{border-right-color:#fff;bottom:10px;left:-10px}.introjs-tooltip{background-color:#fff;border-radius:5px;box-shadow:0 3px 30px #2121214d;box-sizing:initial;max-width:300px;min-width:250px;position:absolute;transition:opacity .1s ease-out;visibility:visible}.introjs-tooltiptext{padding:20px}.introjs-tooltip-title{float:left;font-size:18px;font-weight:700;line-height:32px;margin:0;padding:0}.introjs-tooltip-header{padding-left:20px;padding-right:20px;padding-top:10px}.introjs-tooltip-header:after{clear:both;content:".";display:block;height:0;visibility:hidden}.introjs-tooltipbuttons{border-top:1px solid #e0e0e0;padding:10px;text-align:right;white-space:nowrap}.introjs-tooltipbuttons:after{clear:both;content:"";display:block;height:0;visibility:hidden}.introjs-button{background-color:#f4f4f4;border:1px solid #bdbdbd;border-radius:.2em;box-sizing:initial;color:#424242;cursor:pointer;display:inline-block;font-size:14px;outline:0;overflow:visible;padding:.5rem 1rem;position:relative;-webkit-text-decoration:none;text-decoration:none;text-shadow:1px 1px 0 #fff;white-space:nowrap;zoom:1}.introjs-button:hover{background-color:#e0e0e0;border-color:#9e9e9e}.introjs-button:focus,.introjs-button:hover{color:#212121;outline:0;-webkit-text-decoration:none;text-decoration:none}.introjs-button:focus{background-color:#eee;border:1px solid #616161;box-shadow:0 0 0 .2rem #9e9e9e80}.introjs-button:active{background-color:#e0e0e0;border-color:#9e9e9e;color:#212121;outline:0;-webkit-text-decoration:none;text-decoration:none}.introjs-button::-moz-focus-inner{border:0;padding:0}.introjs-skipbutton{box-sizing:initial;color:#616161;cursor:pointer;float:right;font-size:20px;font-weight:700;line-height:1;padding:7px 10px;text-align:center}.introjs-skipbutton:focus,.introjs-skipbutton:hover{color:#212121;outline:0;-webkit-text-decoration:none;text-decoration:none}.introjs-prevbutton{float:left}.introjs-nextbutton{float:right}.introjs-disabled,.introjs-disabled:focus,.introjs-disabled:hover{background-color:#f4f4f4;background-image:none;border-color:#bdbdbd;box-shadow:none;color:#9e9e9e;cursor:default;-webkit-text-decoration:none;text-decoration:none}.introjs-hidden{display:none}.introjs-bullets{padding-bottom:10px;padding-top:10px;text-align:center}.introjs-bullets ul{box-sizing:initial;clear:both;display:inline-block;margin:0 auto;padding:0}.introjs-bullets ul li{box-sizing:initial;float:left;list-style:none;margin:0 2px}.introjs-bullets ul li a{background:#ccc;border-radius:10px;box-sizing:initial;cursor:pointer;display:block;height:6px;-webkit-text-decoration:none;text-decoration:none;transition:width .1s ease-in;width:6px}.introjs-bullets ul li a:focus,.introjs-bullets ul li a:hover{background:#999;outline:0;-webkit-text-decoration:none;text-decoration:none;width:15px}.introjs-bullets ul li a.active{background:#999;width:15px}.introjs-progress{background-color:#e0e0e0;border-radius:4px;box-sizing:initial;height:10px;margin:10px;overflow:hidden}.introjs-progressbar{background-color:#08c;box-sizing:initial;float:left;font-size:10px;height:100%;line-height:10px;text-align:center;width:0}.introjsFloatingElement{height:0;left:50%;position:absolute;top:50%;width:0}.introjs-fixedTooltip{position:fixed}.introjs-hint{background:0 0;box-sizing:initial;cursor:pointer;height:15px;position:absolute;width:20px}.introjs-hint:focus{border:0;outline:0}.introjs-hint:hover>.introjs-hint-pulse{border:5px solid #3c3c3c91}.introjs-hidehint{display:none}.introjs-fixedhint{position:fixed}.introjs-hint-pulse{background-color:#8888883d;border:5px solid #3c3c3c45;border-radius:30px;box-sizing:initial;height:10px;position:absolute;transition:all .2s ease-out;width:10px;z-index:10}.introjs-hint-no-anim .introjs-hint-dot{animation:none}.introjs-hint-dot{animation:introjspulse 3s ease-out;animation-iteration-count:infinite;background:0 0;border:10px solid #9292925c;border-radius:60px;box-sizing:initial;height:50px;left:-25px;opacity:0;position:absolute;top:-25px;width:50px;z-index:1}
