String.prototype.className=function(){return"string"!=typeof this&&!this instanceof String||"function"!=typeof this.replace?this:this.replace(".","")},String.prototype.varName=function(){return"string"!=typeof this&&!this instanceof String||"function"!=typeof this.replace?this:this.replace("-","_")},function(){const t=new URL(window.location.href).hash;if(!t||!t.match("#(tribe|tec)"))return;let e=!0;const n=new MutationObserver((function(){e=!0}));n.observe(window.document,{attributes:!0,childList:!0,characterData:!0,subtree:!0});const i=function(){if(e)e=!1,setTimeout(i,250);else{n.takeRecords(),n.disconnect();const e=document.getElementById(t.substring(1));e&&e.scrollIntoView()}};i()}(),window.tribe=window.tribe||{},window.tec=window.tec||{},window.tec=window.tec||{},window.tec.common=window.tec.common||{},window.tec.common.tribeCommon={};