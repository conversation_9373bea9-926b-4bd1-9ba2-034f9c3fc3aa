!function(t){"use strict";t((function(){t("#tribe-migrate-ical-settings").on("click",(function(){let e,i=t(this),n=i.next(".spinner"),s=i.parents(".notice").eq(0).find(".notice-dismiss"),c=i.parent();"tribe-migrate-ical-settings"===i.attr("id")&&(e="tribe_convert_legacy_ical_settings"),n.css({visibility:"visible"}),t.ajax(ajaxurl,{dataType:"json",method:"POST",data:{action:e},success(e,i){e.status?(c.html(e.text),setTimeout((function(){s.trigger("click")}),5e3)):c.before(t("<p>").html(e.text))},complete(){n.css({visibility:"hidden"})}})}))}))}(jQuery),window.tec=window.tec||{},window.tec.events=window.tec.events||{},window.tec.events.aggregatorAdminLegacySettings={};