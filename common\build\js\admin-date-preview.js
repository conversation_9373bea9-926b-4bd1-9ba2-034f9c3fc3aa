jQuery((function(i){i(".live-date-preview").siblings("input").on("change",(function(){const n=i(this),t=n.val(),e=n.siblings(".live-date-preview");e.append("<span class='spinner'></span>"),e.find(".spinner").css("visibility","visible");const o={action:"date_format",date:t};i.post(ajaxurl,o,(function(n){n=i("<div/>").html(n).text(),e.html(n)}),"text")}))})),window.tec=window.tec||{},window.tec.common=window.tec.common||{},window.tec.common.adminDatePreview={};