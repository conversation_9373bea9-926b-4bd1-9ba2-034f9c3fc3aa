window.tribe_timepickers=window.tribe_timepickers||{},function(e,t){"use strict";t.selector={container:".tribe-datetime-block",timepicker:".tribe-timepicker",all_day:"#allDayCheckbox",timezone:".tribe-field-timezone",input:"select, input"},t.timepicker={opts:{forceRoundTime:!1,step:30}},t.timezone={link:_.template('<a href="#" class="tribe-change-timezone"><%= label %> <%= timezone %></a>')},t.$={},t.container=function(i,n){const o=e(n),c=o.find(t.selector.all_day),r=o.find(t.selector.timepicker);let a=o.find(t.selector.timezone).not(t.selector.input);const m=o.find(t.selector.timezone).filter(t.selector.input),s=e(t.timezone.link({label:m.data("timezoneLabel"),timezone:m.data("timezoneValue")}));c.on("change",(function(){!0===c.prop("checked")?r.hide():r.show()})).trigger("change"),t.setup_timepickers(r),s.on("click",(function(e){a=o.find(t.selector.timezone).filter(".select2-container"),e.preventDefault(),s.hide(),a.show()})),m.before(s)},t.init=function(){t.$.containers=e(t.selector.container),t.$.containers.each(t.container)},t.setup_timepickers=function(i){i.each((function(){const i=e(this),n=e.extend({},t.timepicker.opts);i.data("format")&&(n.timeFormat=i.data("format")),i.data("step")&&(n.step=i.data("step"));const o=i.data("round");o&&0!=o&&"false"!==o&&(n.forceRoundTime=!0),void 0!==e.fn.tribeTimepicker?i.tribeTimepicker(n).trigger("change"):i.timepicker(n).trigger("change")}))},e(t.init)}(jQuery,window.tribe_timepickers),window.tec=window.tec||{},window.tec.common=window.tec.common||{},window.tec.common.timepicker={};