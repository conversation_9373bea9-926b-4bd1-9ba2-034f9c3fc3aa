/*!
 * @copyright Copyright &copy; <PERSON><PERSON><PERSON>, Krajee.com, 2014 - 2025
 * @version 1.3.6
 *
 * Date formatter utility library that allows formatting date/time variables or Date objects using PHP DateTime format.
 * This library is a standalone javascript library and does not depend on other libraries or plugins like jQuery. The
 * library also adds support for Universal Module Definition (UMD).
 *
 * @see http://php.net/manual/en/function.date.php
 *
 * For more JQuery plugins visit http://plugins.krajee.com
 * For more Yii related demos visit http://demos.krajee.com
 */
!function(t,e){"function"==typeof define&&define.amd?define([],e):"object"==typeof module&&module.exports?module.exports=e():t.DateFormatter=e()}("undefined"!=typeof self?self:this,function(){var m={DAY:864e5,HOUR:3600,defaults:{dateSettings:{days:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],daysShort:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],months:["January","February","March","April","May","June","July","August","September","October","November","December"],monthsShort:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],meridiem:["AM","PM"],ordinal:function(t){var e=t%10,n={1:"st",2:"nd",3:"rd"};return 1!==Math.floor(t%100/10)&&n[e]?n[e]:"th"}},separators:/[ \-+\/.:@]/g,validParts:/[dDjlNSwzWFmMntLoYyaABgGhHisueTIOPZcrU]/g,intParts:/[djwNzmnyYhHgGis]/g,tzParts:/\b(?:[PMCEA][SDP]T|(?:Australian|Pacific|Mountain|Central|Eastern|Atlantic) (?:Eastern) (?:Standard|Daylight|Prevailing) Time|(?:GMT|UTC)(?:[-+]\d{4})?)\b/g,tzClip:/[^-+\dA-Z]/g},getInt:function(t,e){return parseInt(t,e||10)},compare:function(t,e){return"string"==typeof t&&"string"==typeof e&&t.toLowerCase()===e.toLowerCase()},lpad:function(t,e,n){t=t.toString();return n=n||"0",t.length<e?m.lpad(n+t,e):t},merge:function(t){var e,n;for(t=t||{},e=1;e<arguments.length;e++)if(n=arguments[e])for(var r in n)n.hasOwnProperty(r)&&("object"==typeof n[r]?m.merge(t[r],n[r]):t[r]=n[r]);return t},getIndex:function(t,e){for(var n=0;n<e.length;n++)if(e[n].toLowerCase()===t.toLowerCase())return n;return-1}},t=function(t){var e=this,t=m.merge(m.defaults,t);e.dateSettings=t.dateSettings,e.separators=t.separators,e.validParts=t.validParts,e.intParts=t.intParts,e.tzParts=t.tzParts,e.tzClip=t.tzClip};return t.prototype={constructor:t,getMonth:function(t){var e=m.getIndex(t,this.dateSettings.monthsShort)+1;return e=0===e?m.getIndex(t,this.dateSettings.months)+1:e},parseDate:function(t,e){var n,r,a,u,i,o,s,c,f=this,l=!1,d=!1,g=f.dateSettings,h={date:null,year:null,month:null,day:null,hour:0,min:0,sec:0};if(!t)return null;if(t instanceof Date)return t;if("U"===e)return(a=m.getInt(t))?new Date(1e3*a):t;switch(typeof t){case"number":return new Date(t);case"string":break;default:return null}if(!(n=e.match(f.validParts))||0===n.length)throw new Error("Invalid date format definition.");for(a=n.length-1;0<=a;a--)"S"===n[a]&&n.splice(a,1);for(r=t.replace(f.separators,"\0").split("\0"),a=0;a<r.length;a++)switch(u=r[a],i=m.getInt(u),n[a]){case"y":case"Y":if(!i)return null;s=u.length,h.year=2===s?m.getInt((i<70?"20":"19")+u):i,l=!0;break;case"m":case"n":case"M":case"F":if(isNaN(i)){if(!(0<(s=f.getMonth(u))))return null;h.month=s}else{if(!(1<=i&&i<=12))return null;h.month=i}l=!0;break;case"d":case"j":if(!(1<=i&&i<=31))return null;h.day=i,l=!0;break;case"g":case"h":if(c=r[o=-1<n.indexOf("a")?n.indexOf("a"):-1<n.indexOf("A")?n.indexOf("A"):-1],-1!==o)o=m.compare(c,g.meridiem[0])?0:m.compare(c,g.meridiem[1])?12:-1,1<=i&&i<=12&&-1!=o?h.hour=i%12==0?o:i+o:0<=i&&i<=23&&(h.hour=i);else{if(!(0<=i&&i<=23))return null;h.hour=i}d=!0;break;case"G":case"H":if(!(0<=i&&i<=23))return null;h.hour=i,d=!0;break;case"i":if(!(0<=i&&i<=59))return null;h.min=i,d=!0;break;case"s":if(!(0<=i&&i<=59))return null;h.sec=i,d=!0}if(!0===l)h.date=new Date(h.year||0,h.month?h.month-1:0,h.day||1,h.hour,h.min,h.sec,0);else{if(!0!==d)return null;h.date=new Date(0,0,0,h.hour,h.min,h.sec,0)}return h.date},guessDate:function(t,e){if("string"!=typeof t)return t;var n,r,a,u,i,o,s=t.replace(this.separators,"\0").split("\0"),c=e.match(this.validParts),f=new Date,l=0;if(!/^[djmn]/g.test(c[0]))return t;for(a=0;a<s.length;a++){if(i=s[a],o=m.getInt(i.substr(0,l=2)),isNaN(o))return null;switch(a){case 0:"m"===c[0]||"n"===c[0]?f.setMonth(o-1):f.setDate(o);break;case 1:"m"===c[0]||"n"===c[0]?f.setDate(o):f.setMonth(o-1);break;case 2:if(r=f.getFullYear(),l=(n=i.length)<4?n:4,!(r=m.getInt(n<4?r.toString().substr(0,4-n)+i:i.substr(0,4))))return null;f.setFullYear(r);break;case 3:f.setHours(o);break;case 4:f.setMinutes(o);break;case 5:f.setSeconds(o)}0<(u=i.substr(l)).length&&s.splice(a+1,0,u)}return f},parseFormat:function(t,r){function e(t,e){return i[t]?i[t]():e}var n=this,a=n.dateSettings,u=/\\?(.?)/gi,i={d:function(){return m.lpad(i.j(),2)},D:function(){return a.daysShort[i.w()]},j:function(){return r.getDate()},l:function(){return a.days[i.w()]},N:function(){return i.w()||7},w:function(){return r.getDay()},z:function(){var t=new Date(i.Y(),i.n()-1,i.j()),e=new Date(i.Y(),0,1);return Math.round((t-e)/m.DAY)},W:function(){var t=new Date(i.Y(),i.n()-1,i.j()-i.N()+3),e=new Date(t.getFullYear(),0,4);return m.lpad(1+Math.round((t-e)/m.DAY/7),2)},F:function(){return a.months[r.getMonth()]},m:function(){return m.lpad(i.n(),2)},M:function(){return a.monthsShort[r.getMonth()]},n:function(){return r.getMonth()+1},t:function(){return new Date(i.Y(),i.n(),0).getDate()},L:function(){var t=i.Y();return t%4==0&&t%100!=0||t%400==0?1:0},o:function(){var t=i.n(),e=i.W();return i.Y()+(12===t&&e<9?1:1===t&&9<e?-1:0)},Y:function(){return r.getFullYear()},y:function(){return i.Y().toString().slice(-2)},a:function(){return i.A().toLowerCase()},A:function(){var t=i.G()<12?0:1;return a.meridiem[t]},B:function(){var t=r.getUTCHours()*m.HOUR,e=60*r.getUTCMinutes(),n=r.getUTCSeconds();return m.lpad(Math.floor((t+e+n+m.HOUR)/86.4)%1e3,3)},g:function(){return i.G()%12||12},G:function(){return r.getHours()},h:function(){return m.lpad(i.g(),2)},H:function(){return m.lpad(i.G(),2)},i:function(){return m.lpad(r.getMinutes(),2)},s:function(){return m.lpad(r.getSeconds(),2)},u:function(){return m.lpad(1e3*r.getMilliseconds(),6)},e:function(){return/\((.*)\)/.exec(String(r))[1]||"Coordinated Universal Time"},I:function(){return new Date(i.Y(),0)-Date.UTC(i.Y(),0)!=new Date(i.Y(),6)-Date.UTC(i.Y(),6)?1:0},O:function(){var t=r.getTimezoneOffset(),e=Math.abs(t);return(0<t?"-":"+")+m.lpad(100*Math.floor(e/60)+e%60,4)},P:function(){var t=i.O();return t.substr(0,3)+":"+t.substr(3,2)},T:function(){return(String(r).match(n.tzParts)||[""]).pop().replace(n.tzClip,"")||"UTC"},Z:function(){return 60*-r.getTimezoneOffset()},c:function(){return"Y-m-d\\TH:i:sP".replace(u,e)},r:function(){return"D, d M Y H:i:s O".replace(u,e)},U:function(){return r.getTime()/1e3||0}};return e(t,t)},formatDate:function(t,e){var n,r,a,u,i="";if("string"==typeof t&&!(t=this.parseDate(t,e)))return null;if(t instanceof Date){for(r=e.length,n=0;n<r;n++)"S"!==(u=e.charAt(n))&&"\\"!==u&&(0<n&&"\\"===e.charAt(n-1)?i+=u:(a=this.parseFormat(u,t),n!==r-1&&this.intParts.test(u)&&"S"===e.charAt(n+1)&&(u=m.getInt(a)||0,a+=this.dateSettings.ordinal(u)),i+=a));return i}return""}},Object.freeze(t),t});