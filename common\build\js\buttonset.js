window.tribe_buttonset=window.tribe_buttonset||{},function(t,e){"use strict";e.$body,e.selector={buttonset:".tribe-buttonset",button:".tribe-button-field",input:".tribe-button-input",active:".tribe-active"},e.ready=function(n){e.$body=t("body"),e.$body.on("click.tribe_buttonset",e.selector.button,e.click),e.$body.on("change.tribe_buttonset",e.selector.input,e.change).find(e.selector.input).trigger("change")},e.change=function(n){const o=t(this),i=o.val();o.parents(e.selector.buttonset).eq(0).find('[data-value="'+i+'"]').addClass(e.selector.active.replace(".",""))},e.click=function(n){let o,i,c=t(this);o=c.is("[data-group]")?t(c.data("group")):c.parents(e.selector.buttonset);let a=o.length>0,r=o.data("input")?o.data("input"):e.selector.input,s=c.data("value"),d=o.is("[data-multiple]");return a&&!d&&o.find(e.selector.button).removeClass(e.selector.active.replace(".","")),d?c.toggleClass(e.selector.active.replace(".","")):c.addClass(e.selector.active.replace(".","")),c.is("[data-input]")&&(r=c.data("input")),i=c.find(r),a&&0===i.length&&(i=o.find(r)),0===i.length&&(i=t(r)),c.is("[data-value]")&&i.val(s),"checkbox"===i.attr("type")?i.prop("checked",c.is(e.selector.active)):i.prop("disabled",!c.is(e.selector.active)),i.trigger("change"),n.preventDefault(),!1},t(e.ready)}(jQuery,window.tribe_buttonset,window.underscore||window._),window.tec=window.tec||{},window.tec.common=window.tec.common||{},window.tec.common.buttonset={};