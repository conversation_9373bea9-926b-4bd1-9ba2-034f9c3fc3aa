tribe.helpPage=tribe.helpPage||{},function(e,t){"use strict";t.selectors={copyButton:".system-info-copy-btn",optInMsg:".tribe-sysinfo-optin-msg",autoInfoOptIn:"#tribe_auto_sysinfo_opt_in",accordion:".tec-ui-accordion",openSupportChat:"[data-open-support-chat]",helpHubIframe:"#tec-settings__support-hub-iframe",iframeLoader:"#tec-settings__support-hub-iframe-loader",modalButtonSpan:"#tec-settings-nav-modal-open span",navLinkText:".tec-nav__link"},t.setup=()=>{t.setupSystemInfo(),t.setupCopyButton(),t.setupTabs(),t.IframeSupportChatClickHandler(),t.<PERSON>rame<PERSON>()},t.Iframe<PERSON>ender=()=>{const e=document.querySelector(t.selectors.helpHubIframe),o=document.querySelector(t.selectors.iframeLoader);e&&e.addEventListener("load",(()=>{e.classList.remove("hidden"),o&&o.classList.add("hidden")}))},t.sendMessageToIframe=e=>{document.querySelector(t.selectors.helpHubIframe).contentWindow&&document.querySelector(t.selectors.helpHubIframe).contentWindow.postMessage(e,window.origin)},t.openSupportChatInIframe=e=>{e.preventDefault(),t.sendMessageToIframe({action:"runScript",data:"openLivechat"})},t.IframeSupportChatClickHandler=()=>{const e=document.querySelector(t.selectors.openSupportChat);e&&e.addEventListener("click",(e=>t.openSupportChatInIframe(e)))},t.setupAccordionsFor=o=>{e.fn.accordion?e(o).find(t.selectors.accordion).accordion({active:!0,collapsible:!0,heightStyle:"content",icons:{header:"ui-icon-plus",activeHeader:"ui-icon-minus"}}):console.error("jQuery UI Accordion library is missing.")},t.setupCopyButton=()=>{if("undefined"==typeof tribe_system_info)return;const o=new ClipboardJS(t.selectors.copyButton),n='<span class="dashicons dashicons-clipboard license-btn"></span>',s=tribe_system_info.clipboard_btn_text;e(".system-info-copy-btn").on("click",(e=>{e.preventDefault()})),o.on("success",(e=>{e.clearSelection(),e.trigger.innerHTML=n+'<span class="optin-success">'+tribe_system_info.clipboard_copied_text+"<span>",window.setTimeout((function(){e.trigger.innerHTML=n+s}),5e3)})),o.on("error",(e=>{e.trigger.innerHTML=n+'<span class="optin-fail">'+tribe_system_info.clipboard_fail_text+"<span>",window.setTimeout((()=>{e.trigger.innerHTML=n+s}),5e3)}))},t.setupSystemInfo=()=>{"undefined"!=typeof tribe_system_info&&(t.$system_info_opt_in=e(t.selectors.autoInfoOptIn),t.$system_info_opt_in_msg=e(t.selectors.optInMsg),t.$system_info_opt_in.on("change",(()=>{this.checked?t.doAjaxRequest("generate"):t.doAjaxRequest("remove")})))},t.doAjaxRequest=o=>{const n={action:"tribe_toggle_sysinfo_optin",confirm:tribe_system_info.sysinfo_optin_nonce,generate_key:o};e.post(ajaxurl,n,(o=>{if(o.success)t.$system_info_opt_in_msg.html("<p class='optin-success'>"+o.data+"</p>");else{let n="<p class='optin-fail'>"+tribe_system_info.sysinfo_error_message_text+"</p>";o.data&&(o.data.message?n+="<p>"+o.data.message+"</p>":o.message&&(n+="<p>"+o.message+"</p>"),o.data.code&&(n+="<p>"+tribe_system_info.sysinfo_error_code_text+" "+o.data.code+"</p>"),o.data.status&&(n+="<p>"+tribe_system_info.sysinfo_error_status_text+o.data.status+"</p>")),t.$system_info_opt_in_msg.html(n),e(t.selectors.autoInfoOptIn).prop("checked",!1)}}))},t.setupTabs=()=>{const e=document.querySelectorAll("[data-tab-target]");document.querySelectorAll(".tec-tab-container").forEach((e=>{e.classList.add("hidden")}));const o=document.querySelector(".tec-nav__tab.tec-nav__tab--active"),n=o?document.getElementById(o.getAttribute("data-tab-target")):null;if(o){const e=o.querySelector(t.selectors.navLinkText).textContent.trim(),n=document.querySelector(t.selectors.modalButtonSpan);n&&(n.textContent=e)}n&&n.classList.remove("hidden"),t.setupTabEventListeners(e,n)},t.setupTabEventListeners=(e,o)=>{t.activeTabContainer=o,document.addEventListener("click",(o=>{const n=o.target.closest("[data-tab-target]");if(!n)return;const s=n.getAttribute("data-tab-target");t.updateActiveTab(e,s),t.updateActiveContent(n,s)}))},t.updateActiveTab=(e,t)=>{e.forEach((e=>e.classList.remove("tec-nav__tab--active"))),document.querySelectorAll(`[data-tab-target="${t}"]`).forEach((e=>{e.classList.add("tec-nav__tab--active")}))},t.updateActiveContent=(e,o)=>{t.activeTabContainer&&t.activeTabContainer.classList.add("hidden");const n=document.getElementById(o);if(n){n.classList.remove("hidden"),t.activeTabContainer=n;const e=n.getAttribute("data-link-title"),o=document.querySelector(t.selectors.modalButtonSpan);o&&e&&(o.textContent=e),t.setupAccordionsFor(t.activeTabContainer)}},e(t.setup)}(jQuery,tribe.helpPage),window.tec=window.tec||{},window.tec.common=window.tec.common||{},window.tec.common.admin=window.tec.common.admin||{},window.tec.common.admin.helpPage={};