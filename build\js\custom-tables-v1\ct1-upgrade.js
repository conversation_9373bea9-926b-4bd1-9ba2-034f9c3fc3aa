(()=>{"use strict";var e={d:(t,n)=>{for(var r in n)e.o(n,r)&&!e.o(t,r)&&Object.defineProperty(t,r,{enumerable:!0,get:n[r]})},o:(e,t)=>Object.prototype.hasOwnProperty.call(e,t),r:e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})}},t={};e.r(t),e.d(t,{ajaxGet:()=>u,bindNodes:()=>M,buildQueryString:()=>g,cancelReportPoll:()=>j,getReport:()=>P,getUpgradeBoxElement:()=>p,handleCancelMigration:()=>E,handleReportData:()=>h,handleRevertMigration:()=>L,handleStartMigration:()=>k,handleStartMigrationWithPreview:()=>w,init:()=>B,isNodeDiff:()=>x,onError:()=>f,onFailure:()=>v,onSuccess:()=>m,pollForReport:()=>b,recursePollForReport:()=>y,selectors:()=>l,shouldPoll:()=>_,startMigration:()=>S,syncReportData:()=>T,undoMigration:()=>C});let n,r,a=null,o=null,i=null,c=5e3,s=null,d={poll:!0};const l={upgradeBox:"#tec-ct1-upgrade-dynamic",startPreviewButton:".tec-ct1-upgrade-start-migration-preview",startMigrationButton:".tec-ct1-upgrade-start-migration",cancelMigrationButton:".tec-ct1-upgrade-cancel-migration",revertMigrationButton:".tec-ct1-upgrade-revert-migration",paginateButton:"[data-events-paginate]"},g=(e={})=>{if(!(e instanceof Object&&!Array.isArray(e)||"string"==typeof e))throw new Error("data must be an object or a string");if("string"==typeof e){const t={};e.split("&").map((e=>{const[n,r]=e.split("=",2);t[n]=r})),e=t}const t=Object.keys(e).map((function(t){return encodeURIComponent(t)+"="+encodeURIComponent(e[t])})).join("&");return t?"?"+t:""},u=(e,t={},n,r,a)=>{if(!e)return;const o=e+g(t),i=new XMLHttpRequest;i.open("GET",o,!0),i.onreadystatechange=function(){if(i.readyState===XMLHttpRequest.DONE){const e=i.status;if(0===e||e>=200&&e<400)try{n&&n(JSON.parse(this.response))}catch(e){a&&a(this.response)}else r&&r(this.response)}},i.onerror=function(){a&&a()},i.send()},p=()=>document.getElementById(l.upgradeBox.substr(1)),m=()=>{},v=()=>{},f=()=>{},y=()=>{T(b)},_=()=>d.poll||tecCt1Upgrade.forcePolling,b=()=>{_()&&(s=setTimeout(y,c))},h=function(e){const{nodes:t,key:n,html:r}=e;"stop"!==n?(d.key&&d.key===n||(p().innerHTML=r,M(n)),t.forEach((e=>{if(x(e.key,e.hash)){const t=document.querySelector(e.target);t&&(e.prepend?t.innerHTML=e.html+t.innerHTML:e.append?t.innerHTML=t.innerHTML+e.html:t.innerHTML=e.html,M(e.key))}})),d=e):d.poll=!1},M=e=>{let t;t=document.querySelectorAll(l.startPreviewButton),t&&t.forEach((function(e){e.removeEventListener("click",w),e.addEventListener("click",w)})),t=document.querySelectorAll(l.startMigrationButton),t&&t.forEach((function(e){e.removeEventListener("click",k),e.addEventListener("click",k)})),t=document.querySelectorAll(l.cancelMigrationButton),t&&t.forEach((function(e){e.removeEventListener("click",E),e.addEventListener("click",E)})),t=document.querySelectorAll(l.revertMigrationButton),t&&t.forEach((function(e){e.removeEventListener("click",L),e.addEventListener("click",L)})),t=document.querySelectorAll(l.paginateButton),t&&t.forEach((function(e){e.removeEventListener("click",U(e)),e.addEventListener("click",U(e))}))},U=e=>t=>{t.preventDefault();const a=!!e.dataset.eventsPaginateUpcoming,o=e.dataset.eventsPaginateCategory,i=e.dataset.eventsPaginateStartPage;a?r||(r=i):n||(n=i);const c=a?r++:n++;u(tecCt1Upgrade.ajaxUrl,{action:tecCt1Upgrade.actions.paginateEvents,page:c,upcoming:a?1:0,report_category:o,_ajax_nonce:tecCt1Upgrade.nonce},(({html:e,append:n,prepend:r,has_more:a})=>{const i=document.querySelector(`.tec-ct1-upgrade-events-category-${o}`);if(i.innerHTML=r?e+i.innerHTML:n?i.innerHTML+e:e,!a){const e=document.querySelector(".tec-ct1-upgrade-migration-pagination-separator");e&&e.remove(),t.target.remove()}}))},E=e=>{e.preventDefault(),confirm(tecCt1Upgrade.text_dictionary.confirm_cancel_migration)&&(e.target.setAttribute("disabled","disabled"),e.target.removeEventListener("click",E),C(tecCt1Upgrade.actions.cancelMigration))},L=e=>{e.preventDefault(),confirm(tecCt1Upgrade.text_dictionary.confirm_revert_migration)&&(e.target.setAttribute("disabled","disabled"),e.target.removeEventListener("click",L),C(tecCt1Upgrade.actions.revertMigration))},C=e=>{j(),u(tecCt1Upgrade.ajaxUrl,{action:e,_ajax_nonce:tecCt1Upgrade.nonce},(e=>{h(e),b()}))},w=e=>{e.preventDefault(),e.target.setAttribute("disabled","disabled"),e.target.removeEventListener("click",w),S(!0)},k=e=>{e.preventDefault();const t=tecCt1Upgrade.text_dictionary.migration_in_progress_paragraph+" "+tecCt1Upgrade.text_dictionary.migration_prompt_plugin_state_addendum;confirm(t)&&(e.target.setAttribute("disabled","disabled"),e.target.removeEventListener("click",k),S(!1))},S=e=>{j(),u(tecCt1Upgrade.ajaxUrl,{action:tecCt1Upgrade.actions.startMigration,tec_events_custom_tables_v1_migration_dry_run:e?1:0,_ajax_nonce:tecCt1Upgrade.nonce},(e=>{h(e),b()}))},j=()=>{clearTimeout(s)},x=(e,t)=>{const{nodes:n}=d;if(!n)return!0;const r=n.find((({key:t})=>t===e));return!r||r.hash!==t},T=function(e=null){P((function(t){h(t),e&&e(t)}))},P=e=>{const t={action:tecCt1Upgrade.actions.getReport,_ajax_nonce:tecCt1Upgrade.nonce};tecCt1Upgrade.isMaintenanceMode&&(t.is_maintenance_mode="1"),u(tecCt1Upgrade.ajaxUrl,t,e)},B=()=>{o=window.tecCt1Upgrade,o&&(a=p(!0),i=o.ajaxUrl,c=o.pollInterval||c,0!==c&&T(b))};"loading"!==document.readyState?B():document.addEventListener("DOMContentLoaded",B),window.tec=window.tec||{},window.tec.events=window.tec.events||{},window.tec.events.customTablesV1=window.tec.events.customTablesV1||{},window.tec.events.customTablesV1.ct1Upgrade=t})();