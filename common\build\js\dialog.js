window.tribe=window.tribe||{},window.tribe.dialogs=window.tribe.dialogs||{},function(e,o){"use strict";o.dialogs=o.dialogs||[],o.events=o.events||{},o.getDialogName=function(e){return"dialog_obj_"+e.id},o.init=function(){o.dialogs.forEach((function(t){const n=o.getDialogName(t),i=new window.tec.common.tecA11yDialog({appendTarget:t.appendTarget,bodyLock:t.bodyLock,closeButtonAriaLabel:t.closeButtonAriaLabel,closeButtonClasses:t.closeButtonClasses,contentClasses:t.contentClasses,effect:t.effect,effectEasing:t.effectEasing,effectSpeed:t.effectSpeed,overlayClasses:t.overlayClasses,overlayClickCloses:t.overlayClickCloses,trigger:t.trigger,wrapperClasses:t.wrapperClasses});window[n]=i,t.a11yInstance=i,window[n].on("show",(function(n,i){i&&(i.preventDefault(),i.stopPropagation()),e(o.events).trigger(t.showEvent,[n,i])})),window[n].on("hide",(function(n,i){i&&(i.preventDefault(),i.stopPropagation()),e(o.events).trigger(t.closeEvent,[n,i])}))}))},e(o.init)}(jQuery,window.tribe.dialogs),window.tec=window.tec||{},window.tec.common=window.tec.common||{},window.tec.common.dialog={};