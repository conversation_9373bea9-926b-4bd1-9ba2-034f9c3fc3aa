tribe.events=tribe.events||{},tribe.events.admin=tribe.events.admin||{},tribe.events.admin.categoryColors={},((e,t)=>{"use strict";const i=e(document),o=t.selectors={colorInput:".tec-events-category-colors__input.wp-color-picker",primaryColor:'[name="tec_events_category-color[primary]"]',backgroundColor:'[name="tec_events_category-color[secondary]"]',fontColor:'[name="tec_events_category-color[text]"]',previewText:".tec-events-category-colors__preview-text",tableColorPreview:".column-category_color .tec-events-taxonomy-table__category-color-preview",tagName:'input[name="tag-name"], input[name="name"]',priorityField:'input[name="tec_events_category-color[priority]"]',form:e("#addtag").length?"#addtag":"#edittag",hideFromLegendField:'[name="tec_events_category-color[hide_from_legend]"]',quickEditButton:".editinline",quickEditRow:".inline-edit-row",quickEditSave:".inline-edit-save .save",quickEditCancel:".inline-edit-save .cancel",colorContainer:".tec-events-category-colors__container",wpPickerContainer:".wp-picker-container",irisPicker:".iris-picker",colorResult:".wp-color-result",initializedClass:"wp-color-picker-initialized"},n=e=>e.find(o.colorInput),r=t=>{if(!t)return;const i=e(t),n=i.closest(".tec-events-category-colors__wrap, form, .inline-edit-row").find(o.previewText),r=n.data("default-text")||"",a=i.val().trim();n.text(a.length?a:r)},a=(e=>{let t=null;return function(...i){clearTimeout(t),t=setTimeout((()=>e.apply(this,i)),100)}})(r),c=e=>{if(!e||e.prop("disabled")||e.prop("readonly"))return;const t=e.closest(o.colorContainer),i=t.find(o.primaryColor).val()||"transparent",n=t.find(o.backgroundColor).val()||"transparent",r=t.find(o.fontColor).val()||"inherit";t.find(o.previewText).css({"border-left":`5px solid ${i}`,"background-color":n}),t.find(o.previewText).css({color:r})},l=e=>{e.prop("disabled")||e.prop("readonly")||e.hasClass(o.initializedClass)||(e.wpColorPicker({change:function(){c(e)},clear:function(){c(e)}}),e.iris("color",e.val()))},s=()=>{i.on("input",o.colorInput,(function(){e(this).prop("disabled")||e(this).prop("readonly")||c(e(this))})),i.on("input change",o.tagName,(function(t){e(t.target).prop("disabled")||e(t.target).prop("readonly")||a(t.target)})),i.on("click",o.quickEditButton,(function(){const t=e(this).closest("tr").find(o.tableColorPreview),i={primary:t.data("primary")||"",secondary:t.data("secondary")||"",text:t.data("text")||""};setTimeout((()=>{const n=e(o.quickEditRow+":visible");["primary","secondary","text"].forEach((e=>{const t=n.find(`[name="tec_events_category-color[${e}]"]`);t.length&&(t.val(i[e]).attr("value",i[e]),t.hasClass(o.initializedClass)||l(t),t.iris&&t.iris("color",i[e]),c(t))})),n.find(o.priorityField).val(t.data("priority")||""),n.find(o.hideFromLegendField).prop("checked",!!t.data("hidden"));const r=n.find(o.tagName);!r.length||r.prop("disabled")||r.prop("readonly")||a(r[0])}),10)})),i.on("click",o.quickEditCancel,(function(){const t=e(this).closest(o.quickEditRow);u(t)}))},d=t=>{n(t).filter(":visible").each((function(){l(e(this))}))},p=t=>{t.find(o.tagName).each((function(){e(this).prop("disabled")||e(this).prop("readonly")||r(this)})),n(t).each((function(){const t=e(this);t.prop("disabled")||t.prop("readonly")||c(t)}))};i.ready((()=>{setTimeout((()=>{const t=e("body");d(t),p(t)}),50),s()}));const u=t=>{t.find(o.colorInput).each((function(){const t=e(this);if(t.hasClass(o.initializedClass)){const e=t.clone();t.closest(o.wpPickerContainer).replaceWith(e)}}))};if("undefined"!=typeof inlineEditTax){const t=inlineEditTax.open;inlineEditTax.open=function(i){u(jQuery("#inline-edit")),t.apply(this,arguments);const n=jQuery(o.quickEditRow),r=jQuery(".inline-edit-row"),s=n;r.not(s).each((function(){u(e(this)),e(this).remove()}));const d=jQuery(`#tag-${i}`).find(o.tableColorPreview),p={primary:d.data("primary")||"",secondary:d.data("secondary")||"",text:d.data("text")||""};["primary","secondary","text"].forEach((e=>{const t=n.find(`[name="tec_events_category-color[${e}]"]`),i=p[e]||"";if(t.length&&!t.prop("disabled")&&!t.prop("readonly")){const e=t.clone().val(i);t.closest(o.wpPickerContainer).replaceWith(e),l(e),e.iris("color",i),i||e.wpColorPicker("clear"),e.siblings(o.colorResult).css("background-color",i||"transparent"),requestAnimationFrame((()=>c(e)))}})),n.find(o.priorityField).val(d.data("priority")||""),n.find(o.hideFromLegendField).prop("checked",!!d.data("hidden"));const y=n.find(o.tagName);!y.length||y.prop("disabled")||y.prop("readonly")||a(y[0])}}})(jQuery,tribe.events.admin.categoryColors),window.tec=window.tec||{},window.tec.events=window.tec.events||{},window.tec.events.admin=window.tec.events.admin||{},window.tec.events.admin.categoryColors=window.tec.events.admin.categoryColors||{},window.tec.events.admin.categoryColors.adminCategory={};