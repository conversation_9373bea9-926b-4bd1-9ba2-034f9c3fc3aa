tribe.events=tribe.events||{},tribe.events.views=tribe.events.views||{},tribe.events.views.tooltip={},function(e,t){"use strict";const o=e(document);t.config={delayHoverIn:300,delayHoverOut:300},t.selectors={tooltipTrigger:'[data-js~="tribe-events-tooltip"]',tribeEventsTooltipTriggerHoverClass:".tribe-events-tooltip-trigger--hover",tribeEventsTooltipThemeClass:".tribe-events-tooltip-theme",tribeEventsTooltipThemeHoverClass:".tribe-events-tooltip-theme--hover",tribeCommonClass:".tribe-common",tribeEventsClass:".tribe-events"},t.handleOriginFocus=function(e){setTimeout((function(){(e.data.target.is(":focus")||e.data.target.hasClass(t.selectors.tribeEventsTooltipTriggerHoverClass.className()))&&e.data.target.tooltipster("open")}),t.config.delayHoverIn)},t.handleOriginBlur=function(e){e.data.target.tooltipster("close")},t.handleOriginHoverIn=function(e){e.data.target.addClass(t.selectors.tribeEventsTooltipTriggerHoverClass.className())},t.handleOriginHoverOut=function(e){e.data.target.removeClass(t.selectors.tribeEventsTooltipTriggerHoverClass.className())},t.handleTooltipHoverIn=function(e){e.data.target.addClass(t.selectors.tribeEventsTooltipThemeHoverClass.className())},t.handleTooltipHoverOut=function(e){e.data.target.removeClass(t.selectors.tribeEventsTooltipThemeHoverClass.className())},t.handleInstanceClose=function(o){const n=o.data.origin,i=e(o.tooltip);(n.is(":focus")||n.hasClass(t.selectors.tribeEventsTooltipTriggerHoverClass.className())||i.hasClass(t.selectors.tribeEventsTooltipThemeHoverClass.className()))&&o.stop()},t.handleInstanceClosing=function(o){e(o.tooltip).off("mouseenter touchstart",t.handleTooltipHoverIn).off("mouseleave touchleave",t.handleTooltipHoverOut)},t.onFunctionInit=function(o,n){const i=e(n.origin);i.on("focus",{target:i},t.handleOriginFocus).on("blur",{target:i},t.handleOriginBlur).on("mouseenter touchstart",{target:i},t.handleOriginHoverIn).on("mouseleave touchleave",{target:i},t.handleOriginHoverOut),o.on("close",{origin:i},t.handleInstanceClose).on("closing",{origin:i},t.handleInstanceClosing)},t.onFunctionReady=function(o,n){const i=e(n.tooltip);i.on("mouseenter touchstart",{target:i},t.handleTooltipHoverIn).on("mouseleave touchleave",{target:i},t.handleTooltipHoverOut)},t.deinitTooltips=function(o){o.find(t.selectors.tooltipTrigger).each((function(t,o){e(o).off().tooltipster("instance").off()}))},t.initTooltips=function(o){const n=o.data("tribeEventsTooltipTheme");o.find(t.selectors.tooltipTrigger).each((function(o,i){e(i).tooltipster({animationDuration:0,interactive:!0,delay:[t.config.delayHoverIn,t.config.delayHoverOut],delayTouch:[t.config.delayHoverIn,t.config.delayHoverOut],theme:n,functionInit:t.onFunctionInit,functionReady:t.onFunctionReady})}))},t.initTheme=function(e){e.trigger("beforeTooltipInitTheme.tribeEvents",[e]);const o=[t.selectors.tribeEventsTooltipThemeClass.className(),t.selectors.tribeCommonClass.className(),t.selectors.tribeEventsClass.className()];e.data("tribeEventsTooltipTheme",o),e.trigger("afterTooltipInitTheme.tribeEvents",[e])},t.deinit=function(e,o,n){const i=e.data.container;t.deinitTooltips(i),i.off("beforeAjaxSuccess.tribeEvents",t.deinit)},t.init=function(e,o,n,i){t.initTheme(n),t.initTooltips(n),n.on("beforeAjaxSuccess.tribeEvents",{container:n},t.deinit)},t.ready=function(){o.on("afterSetup.tribeEvents",tribe.events.views.manager.selectors.container,t.init)},e(t.ready)}(jQuery,tribe.events.views.tooltip),window.tec=window.tec||{},window.tec.events=window.tec.events||{},window.tec.events.views=window.tec.events.views||{},window.tec.events.views.tooltip={};