(()=>{var e={1230:()=>{}},t={};function n(s){var a=t[s];if(void 0!==a)return a.exports;var i=t[s]={exports:{}};return e[s](i,i.exports,n),i.exports}n.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return n.d(t,{a:t}),t},n.d=(e,t)=>{for(var s in t)n.o(t,s)&&!n.o(e,s)&&Object.defineProperty(e,s,{enumerable:!0,get:t[s]})},n.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),n.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},n.p="/wp-content/plugins/the-events-calendar/build/",(()=>{"use strict";var e={};n.r(e),n.d(e,{getCompletedTabs:()=>m,getIsSaving:()=>g,getSetting:()=>x,getSettings:()=>p,getSkippedTabs:()=>j,getVisitedFields:()=>b});var t={};n.r(t),n.d(t,{completeTab:()=>z,createSetting:()=>F,initializeSettings:()=>E,setSaving:()=>W,setVisitedField:()=>I,skipTab:()=>Z,updateSettings:()=>V});var s={};n.r(s),n.d(s,{getInitialState:()=>ne,getIsOpen:()=>te});var a={};n.r(a),n.d(a,{closeModal:()=>oe,openModal:()=>re});const i=window.React;var r=n.n(i);const o=window.wp.domReady;var d=n.n(o);const l=window.ReactDOM;var c=n.n(l);const h=window.wp.components,u=window.wp.element,v=window.wp.data,_=window.wp.i18n,p=e=>e.settings||{},x=(e,t)=>e.settings[t]||!1,g=e=>e.isSaving||!1,b=e=>e.visitedFields||[],m=e=>e.completedTabs||[],j=e=>e.skippedTabs||[],C={CREATE:"CREATE",INITIALIZE:"INITIALIZE",IS_SAVING:"IS_SAVING",SAVE_SETTINGS_ERROR:"SAVE_SETTINGS_ERROR",SAVE_SETTINGS_REQUEST:"SAVE_SETTINGS_REQUEST",SAVE_SETTINGS_SUCCESS:"SAVE_SETTINGS_SUCCESS",UPDATE:"UPDATE",SET_VISITED_FIELDS:"SET_VISITED_FIELDS",SKIP_TAB:"SKIP_TAB",COMPLETE_TAB:"COMPLETE_TAB"},{CREATE:k,INITIALIZE:L,IS_SAVING:f,SAVE_SETTINGS_ERROR:w,SAVE_SETTINGS_REQUEST:S,SAVE_SETTINGS_SUCCESS:y,UPDATE:T,SET_VISITED_FIELDS:M,SKIP_TAB:N,COMPLETE_TAB:A}=C;function E(e){return{type:L,settings:e}}function F(e){return{type:k,setting:e}}const V=e=>({type:T,settings:e}),W=e=>({type:f,isSaving:e}),I=e=>({type:M,payload:e}),Z=e=>({type:N,payload:e}),z=e=>({type:A,payload:e}),{CREATE:B,INITIALIZE:H,IS_SAVING:D,SAVE_SETTINGS_ERROR:O,SAVE_SETTINGS_REQUEST:R,SAVE_SETTINGS_SUCCESS:P,UPDATE:q,SET_VISITED_FIELDS:U,SKIP_TAB:G,COMPLETE_TAB:Y}=C,$={settings:{},isSaving:!1,error:null,visitedFields:[],completedTabs:[],skippedTabs:[]};var K=n(1230);const Q=window.wp.dataControls,J="data/TOWSettingsStore",X="/tec/v2/onboarding/wizard",ee={selectors:e,actions:t,reducer:(e=$,{settings:t,setting:n,type:s,payload:a,error:i})=>{switch(s){case H:if(e.settings&&Object.keys(e.settings).length>0)return e;const{completedTabs:s=[],skippedTabs:r=[],visitedFields:o=[],...d}=t||{};return{...e,settings:d,completedTabs:s,skippedTabs:r,visitedFields:o};case B:return{...e,settings:{...e.settings,...n&&n.key?{[n.key]:n.value}:{}}};case q:return t?{...e,settings:{...e.settings,...t}}:e;case R:return{...e,isSaving:!0,error:null};case P:return{...e,settings:{...e.settings,...a},isSaving:!1};case O:return{...e,isSaving:!1,error:i};case D:return{...e,isSaving:a||!1};case U:return{...e,visitedFields:Array.from(new Set([...e.visitedFields||[],a]))};case Y:return{...e,completedTabs:Array.from(new Set([...e.completedTabs||[],a])),skippedTabs:e.skippedTabs.filter((e=>e!==a))};case G:return e.completedTabs.includes(a)?e:{...e,skippedTabs:Array.from(new Set([...e.skippedTabs||[],a]))};default:return e}},resolvers:K,controls:Q.controls},te=e=>e.isOpen,ne=()=>!0,se={OPEN_MODAL:"OPEN_MODAL",CLOSE_MODAL:"CLOSE_MODAL"},{OPEN_MODAL:ae,CLOSE_MODAL:ie}=se,re=()=>({type:ae}),oe=()=>({type:ie}),{OPEN_MODAL:de,CLOSE_MODAL:le}=se,ce={isOpen:!1},he="data/TOWModalStore",ue={actions:a,reducer:(e=ce,t)=>{switch(t.type){case de:return{...e,isOpen:!0};case le:return{...e,isOpen:!1};default:return e}},selectors:s};(0,v.registerStore)(J,ee),(0,v.registerStore)(he,ue);const ve=window.ReactJSXRuntime,_e=()=>(0,ve.jsx)("svg",{height:"33",width:"31",className:"tec-events-onboarding__header-icon",viewBox:"0 0 31 33",fill:"none",xmlns:"http://www.w3.org/2000/svg",alt:"The Events Calendar logo",children:(0,ve.jsxs)("g",{id:"EventsCalendar-icon",children:[(0,ve.jsx)("path",{id:"white fill",d:"M1.46839 12.3425C0.887005 10.8 1.6987 9.08841 3.2763 8.53026L21.8859 1.94624C23.4936 1.37744 25.2619 2.22892 25.7859 3.82414L29.5145 15.1769C29.5775 15.369 29.6178 15.5673 29.6254 15.7689C29.7646 19.4824 27.9442 24.9168 22.3104 27.5588L17.2766 29.5331L9.43319 32.0966C9.17544 32.1809 8.89602 32.0494 8.80183 31.7995L1.46839 12.3425Z",fill:"white"}),(0,ve.jsx)("path",{id:"fill",fillRule:"evenodd",clipRule:"evenodd",d:"M26.8824 6.58311L26.0339 4.15491C25.4462 2.48383 23.5743 1.61099 21.8814 2.21808L3.90795 8.66568C2.24865 9.26038 1.38768 11.0556 1.97895 12.6888L2.82935 14.9721L26.8824 6.58311Z",fill:"#334AFF"}),(0,ve.jsx)("path",{id:"Combined Shape",fillRule:"evenodd",clipRule:"evenodd",d:"M18.6651 0.559771C18.5391 0.308651 18.2378 0.185743 17.9635 0.283638C17.6696 0.388526 17.518 0.707166 17.625 0.995341L18.2862 2.77658L6.48262 7.01087L5.83661 5.27052L5.81238 5.21479C5.68645 4.96367 5.38507 4.84076 5.11077 4.93865C4.81687 5.04354 4.66533 5.36218 4.7723 5.65036L5.41896 7.39243L3.45367 8.09744L3.33774 8.14108C1.46279 8.88107 0.504308 10.946 1.18556 12.8278L8.14535 32.0517L8.17344 32.1214C8.39606 32.6215 8.97873 32.8746 9.51126 32.6893L17.1745 29.9405C17.2072 29.9288 17.2382 29.9144 17.2673 29.8976C17.3201 29.8858 17.3721 29.8663 17.4216 29.8386C19.2333 28.8267 20.5916 27.0233 21.7298 24.544C21.9719 24.0167 22.1973 23.4741 22.4319 22.8661L22.4852 22.7272L23.039 21.2443C23.1356 20.9914 23.2097 20.8092 23.2732 20.6707L23.3253 20.562L23.3408 20.5313L23.4798 20.5359C25.7077 20.5952 27.5449 20.1023 28.716 19.2139C27.9027 22.7581 25.3271 25.8338 21.5914 27.1736C21.2977 27.279 21.1466 27.5979 21.2541 27.8859C21.3615 28.1739 21.6867 28.322 21.9805 28.2166C28.413 25.9095 31.72 18.9258 29.367 12.6183L26.3096 3.92706L26.2681 3.81477C25.534 1.92357 23.3795 0.949699 21.4272 1.6498L19.3499 2.39501L18.6893 0.615511L18.6651 0.559771ZM18.6736 3.82015L19.3239 5.57223L19.3482 5.62797C19.4741 5.87909 19.7755 6.002 20.0498 5.90411C20.3437 5.79922 20.4952 5.48058 20.3882 5.1924L19.7372 3.43859L21.8163 2.69276L21.9186 2.65832C23.2863 2.22787 24.7663 2.94553 25.2389 4.28916L28.2998 12.9901L28.3625 13.1625C28.7413 14.2312 28.9432 15.3191 28.9832 16.3951C28.9781 16.428 28.976 16.4619 28.9772 16.4963C29.037 18.2978 26.7696 19.6062 23.159 19.4116C22.6296 19.3832 22.4826 19.5439 22.076 20.5971L21.8913 21.0856L21.5883 21.9032L21.3725 22.4731C21.1455 23.0615 20.9286 23.5837 20.697 24.0881C19.6482 26.3727 18.4207 28.0025 16.861 28.8736L16.854 28.8776C16.831 28.8828 16.8081 28.8894 16.7853 28.8976L9.19141 31.6215L2.25292 12.4563L2.21778 12.3525C1.80243 11.035 2.51018 9.618 3.84266 9.14044L5.80634 8.43601L6.47124 10.2272L6.49547 10.283C6.62141 10.5341 6.92278 10.657 7.19708 10.5591C7.49098 10.4542 7.64252 10.1356 7.53555 9.84742L6.87 8.05444L18.6736 3.82015Z",fill:"#0F1031"}),(0,ve.jsx)("path",{id:"Fill 5",fillRule:"evenodd",clipRule:"evenodd",d:"M21.4009 13.6287C21.6645 14.3355 22.4627 14.6986 23.1835 14.4401C23.9044 14.1816 24.2752 13.3992 24.0114 12.692C23.7478 11.9852 22.9498 11.6216 22.2286 11.8802C21.5079 12.1391 21.1372 12.9219 21.4009 13.6287Z",fill:"#334AFF"}),(0,ve.jsx)("path",{id:"Fill 7",fillRule:"evenodd",clipRule:"evenodd",d:"M15.1743 15.8627C15.4379 16.5695 16.2363 16.933 16.9571 16.6745C17.678 16.4159 18.0488 15.6335 17.785 14.9263C17.5213 14.2195 16.7234 13.8559 16.0022 14.1146C15.2813 14.3731 14.9106 15.1559 15.1743 15.8627Z",fill:"#334AFF"}),(0,ve.jsx)("path",{id:"Fill 9",fillRule:"evenodd",clipRule:"evenodd",d:"M8.94783 18.0952C9.21148 18.802 10.0098 19.1655 10.7307 18.907C11.4514 18.6481 11.8222 17.8657 11.5584 17.1585C11.2945 16.4522 10.4966 16.0886 9.77534 16.3473C9.05448 16.6058 8.68417 17.3884 8.94783 18.0952Z",fill:"#334AFF"}),(0,ve.jsx)("path",{id:"Fill 11",fillRule:"evenodd",clipRule:"evenodd",d:"M17.3859 21.1481C17.6496 21.8549 18.4477 22.218 19.1686 21.9595C19.8895 21.701 20.2599 20.9187 19.9961 20.2115C19.7325 19.5047 18.9345 19.1411 18.2133 19.3997C17.4926 19.6586 17.1219 20.4414 17.3859 21.1481Z",fill:"#334AFF"}),(0,ve.jsx)("path",{id:"Fill 13",fillRule:"evenodd",clipRule:"evenodd",d:"M11.0337 23.4251C11.2974 24.1319 12.0957 24.4954 12.8164 24.2365C13.5373 23.978 13.9081 23.1956 13.6439 22.4885C13.3803 21.7817 12.5825 21.4185 11.8612 21.6771C11.1404 21.9357 10.7701 22.7183 11.0337 23.4251Z",fill:"#334AFF"})]})}),pe=r().memo((({children:e,id:t,tabId:n,tabIndex:s,activeTab:a})=>(0,ve.jsx)("section",{role:"tabpanel",id:t,"aria-labelledby":n,"aria-hidden":a!==s,hidden:a!==s,tabIndex:a===s?0:-1,className:`tec-events-onboarding__tabpanel tec-events-onboarding__tabpanel-${n} ${a===s?"active":""}`,children:e}))),xe=({index:e,tab:t,activeTab:n,handleChange:s})=>{const{id:a,title:r,disabled:o,completed:d,panelId:l,ref:c}=t,h=n===e,u=(0,i.useMemo)((()=>["tec-events-onboarding__tab",`tec-events-onboarding__tab--${a}`,o&&"tec-events-onboarding__tab--disabled",h&&"tec-events-onboarding__tab--active",d&&"tec-events-onboarding__tab--completed"].filter(Boolean).join(" ")),[o,h,d]);return(0,ve.jsx)("li",{role:"presentation",className:u,children:(0,ve.jsx)("button",{"aria-controls":l,"aria-selected":h,className:"tec-events-onboarding__tab-button",disabled:o,id:a,onClick:()=>s(e),ref:c,role:"tab",tabIndex:h?0:-1,children:(0,ve.jsx)("span",{className:"tec-events-onboarding__tab-title",children:r})})})},ge=window.wp.apiFetch;var be=n.n(ge);const me=({tabSettings:e,moveToNextTab:t})=>{const n=(0,v.useDispatch)(J).completeTab,s=(0,v.useDispatch)(J).updateSettings,a=(0,v.useSelect)((e=>e(J).getSetting("_wpnonce")),[]),i=(0,v.useSelect)((e=>e(J).getSettings)),r=(0,v.useSelect)((e=>e(J).getCompletedTabs)),o=(0,v.useSelect)((e=>e(J).getSkippedTabs)),d=(0,v.useSelect)(J).getVisitedFields,[l,c]=(0,u.useState)(!1);return(0,u.useEffect)((()=>{l&&(async()=>{n(e.currentTab),s(e),be().use(be().createNonceMiddleware(a)),(await be()({method:"POST",data:{...i(),completedTabs:r,skippedTabs:o,visitedFields:d()},path:X})).success&&t()})()}),[l]),(0,ve.jsx)(h.Button,{variant:"primary",onClick:c,disabled:!1,className:"tec-events-onboarding__button tec-events-onboarding__button--setup",children:(0,_.__)("Set up my calendar","the-events-calendar")})},je=()=>{const e=(0,v.useDispatch)(he).closeModal,t=(0,v.useSelect)((e=>e(J).getSetting("action_nonce")),[]),n=(0,v.useSelect)((e=>e(J).getSetting("_wpnonce")),[]),[s,a]=(0,u.useState)(!1);return(0,u.useEffect)((()=>{s&&(async()=>{be().use(be().createNonceMiddleware(n)),await be()({method:"POST",data:{finished:!0,begun:!1,action_nonce:t},path:X}),setTimeout((()=>{e()}),1e3)})()}),[s]),(0,ve.jsx)(h.Button,{variant:"tertiary",onClick:()=>a(!0),className:"tec-events-onboarding__button tec-events-onboarding__button--exit",children:(0,_.__)("Skip guided setup","the-events-calendar")})},Ce=({initialOptin:e,onChange:t})=>{const[n,s]=(0,u.useState)(e);return(0,ve.jsxs)("div",{className:"tec-events-onboarding__checkbox tec-events-onboarding__checkbox--optin",children:[(0,ve.jsx)(h.CheckboxControl,{__nextHasNoMarginBottom:!0,"aria-describedby":"tec-events-onboarding__checkbox-description",checked:n,onChange:e=>{s(e),t(e)},id:"tec-events-onboarding__optin-checkbox-input"}),(0,ve.jsxs)("div",{className:"tec-events-onboarding__checkbox-description",children:[(0,ve.jsx)("label",{htmlFor:"tec-events-onboarding__optin-checkbox-input",children:(0,_.__)("Yes, I’d like to share basic information and have access to the TEC chatbot.","the-events-calendar")}),(0,ve.jsx)("div",{id:"tec-events-onboarding__checkbox-description",children:(0,ve.jsx)("a",{href:"https://evnt.is/1bcl",target:"_blank",rel:"noreferrer",children:(0,_.__)("What permissions are being granted?","the-events-calendar")})})]})]})},ke=n.p+"images/wizard-welcome-img.c3798c4a1cb2f42f48e6.png",Le=({moveToNextTab:e,skipToNextTab:t})=>{const n=(0,v.useSelect)((e=>e(J).getSetting("optin")||!1),[]),[s,a]=(0,i.useState)(n),[r,o]=(0,i.useState)(n);(0,i.useEffect)((()=>{o(n)}),[n]);const d={optin:r,currentTab:0,begun:!0};return(0,ve.jsxs)(ve.Fragment,{children:[(0,ve.jsx)("div",{className:"tec-events-onboarding__tab-hero",children:(0,ve.jsx)("img",{src:ke,className:"tec-events-onboarding__welcome-header",alt:"Welcome",role:"presentation"})}),(0,ve.jsxs)("div",{className:"tec-events-onboarding__tab-header",children:[(0,ve.jsx)("h1",{className:"tec-events-onboarding__tab-heading",children:(0,_.__)("Welcome to The Events Calendar","the-events-calendar")}),(0,ve.jsx)("p",{className:"tec-events-onboarding__tab-subheader",children:(0,_.__)("Congratulations on installing the best event management solution for WordPress. Let's tailor your experience to your needs.","the-events-calendar")})]}),(0,ve.jsxs)("div",{className:"tec-events-onboarding__tab-content",children:[(0,ve.jsx)(me,{tabSettings:d,moveToNextTab:e}),(0,ve.jsx)(je,{})]}),(0,ve.jsx)("div",{className:"tec-events-onboarding__tab-footer",children:!s&&(0,ve.jsx)(Ce,{initialOptin:n,onChange:o})})]})},fe=({disabled:e,moveToNextTab:t,tabSettings:n})=>{const s=(0,v.useDispatch)(J).completeTab,{closeModal:a}=(0,v.useDispatch)(he),i=(0,v.useSelect)((e=>e(J).getSetting("action_nonce")),[]),r=(0,v.useSelect)((e=>e(J).getSetting("_wpnonce")),[]),o=(0,v.useDispatch)(J).updateSettings,d=(0,v.useSelect)((e=>e(J).getSettings)),l=(0,v.useSelect)((e=>e(J).getCompletedTabs)),c=(0,v.useSelect)((e=>e(J).getSkippedTabs)),p=(0,v.useSelect)(J).getVisitedFields,[x,g]=(0,u.useState)(!1),[b,m]=(0,u.useState)(!1);return(0,u.useEffect)((()=>{n&&!x&&(g(!1),m(!1))}),[n]),(0,u.useEffect)((()=>{b&&(async()=>{g(!0),5===n.currentTab&&(n.finished=!0),n.action_nonce=i,s(n.currentTab),o(n),be().use(be().createNonceMiddleware(r)),(await be()({method:"POST",data:{...d(),completedTabs:l(),skippedTabs:c(),visitedFields:p()},path:X})).success?(Array.from(document.getElementsByClassName(`tec-events-onboarding-step-${n.currentTab}`)).map((e=>{e.classList.add("tec-admin-page__onboarding-step--completed")})),g(!1),5===n.currentTab?setTimeout((()=>{a()}),1e3):t()):5===n.currentTab&&(g(!1),setTimeout((()=>{a()}),1e3)),g(!1)})()}),[b]),(0,ve.jsx)(ve.Fragment,{children:(0,ve.jsxs)(h.Button,{variant:"primary",disabled:e||x,onClick:()=>m(!0),className:"tec-events-onboarding__button tec-events-onboarding__button--next",children:[x&&(0,_.__)("Saving…","the-events-calendar"),x&&(0,ve.jsx)(h.Spinner,{}),!x&&(0,_.__)("Continue","the-events-calendar")]})})},we=({skipToNextTab:e,currentTab:t,buttonText:n=(0,_.__)("Skip step","the-events-calendar")})=>{const s=(0,v.useDispatch)(J).skipTab,a=(0,v.useDispatch)(he).closeModal,i=(0,v.useSelect)((e=>e(J).getSettings)),r=(0,v.useSelect)((e=>e(J).getCompletedTabs)),o=(0,v.useSelect)((e=>e(J).getSkippedTabs)),d=(0,v.useSelect)(J).getVisitedFields,[l,c]=(0,u.useState)(!1);return(0,u.useEffect)((()=>{l&&(async()=>{s(t);const n=i();5===t&&(n.finished=!0),(await be()({method:"POST",data:{...n,completedTabs:r(),skippedTabs:o(),visitedFields:d()},path:X})).success&&t<5?e():setTimeout((()=>{a()}),1e3)})()}),[l]),(0,ve.jsx)(h.Button,{variant:"tertiary",onClick:()=>c(!0),className:"tec-events-onboarding__button tec-events-onboarding__button--skip",children:n})},Se=({view:e,isChecked:t,onChange:n,icon:s})=>{const a="all"===e?(0,_.__)("Select all the views","the-events-calendar"):e.charAt(0).toUpperCase()+e.slice(1);return(0,ve.jsxs)("div",{id:`tec-events-onboarding__checkbox-${e}`,className:"tec-events-onboarding__checkbox tec-events-onboarding__checkbox--view",children:[(0,ve.jsx)(h.CheckboxControl,{__nextHasNoMarginBottom:!0,"aria-describedby":`tec-events-onboarding__checkbox-label-${e}`,checked:t,onChange:t=>n(e,t),id:`tec-events-onboarding__checkbox-input-${e}`,className:"tec-events-onboarding__checkbox-input",value:e}),(0,ve.jsx)("div",{children:(0,ve.jsxs)("label",{id:`tec-events-onboarding__checkbox-label-${e}`,htmlFor:`tec-events-onboarding__checkbox-input-${e}`,className:t?"tec-events-onboarding__checkbox-label tec-events-onboarding__checkbox-label--checked":"tec-events-onboarding__checkbox-label",children:[s,a]})})]})},ye=()=>(0,ve.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 89 60",alt:"a thumbnail representing the calendar day view",children:[(0,ve.jsx)("rect",{width:"87",height:"59",x:"1",y:".5",fill:"#F6F7F7",rx:"4.495"}),(0,ve.jsx)("rect",{width:"87",height:"59",x:"1",y:".5",stroke:"#DCDCDE",rx:"4.495"}),(0,ve.jsx)("path",{fill:"#0F1031",d:"M12.108 12.91h1.864V8.125l-1.864 1.113V7.645l2.697-1.583h.645v6.847h1.864v1.36h-5.206v-1.36Zm10.214-3.014c.234-.305.398-.567.492-.786.094-.226.14-.46.14-.703 0-.336-.085-.598-.257-.786-.172-.195-.422-.293-.75-.293-.297 0-.54.094-.727.282-.18.187-.293.453-.34.797l-1.36-.258c.023-.414.144-.786.363-1.114.219-.328.512-.586.88-.774a2.556 2.556 0 0 1 1.219-.293c.766 0 1.364.219 1.794.657.437.438.656 1.012.656 1.723 0 .68-.234 1.33-.703 1.947l-2.005 2.614h2.884v1.36H19.39v-.586l2.932-3.787Z"}),(0,ve.jsx)("path",{stroke:"#0F1031",strokeLinecap:"round",strokeLinejoin:"round",d:"M12.088 22.324h25.57M12 26.727h13.317M12 30.727h8"}),(0,ve.jsx)("rect",{width:"20",height:"15",x:"57",y:"19.727",fill:"#fff",rx:".914"}),(0,ve.jsx)("rect",{width:"20",height:"15",x:"57",y:"19.727",stroke:"#C3C4C7",strokeWidth:".914",rx:".914"}),(0,ve.jsx)("path",{fill:"#C3C4C7",d:"m61.852 26.707-4.39 4.899v3.654l14.581.003-8.615-8.624c-.404-.406-1.182-.372-1.576.069Z"}),(0,ve.jsx)("path",{fill:"#C3C4C7",d:"m72.049 28.535-3.214 3.116-3.3 3.267h11v-3.267l-3.214-3.116a.914.914 0 0 0-1.272 0Z"}),(0,ve.jsx)("circle",{cx:"70.905",cy:"24.289",r:"1.371",fill:"#C3C4C7"}),(0,ve.jsx)("path",{stroke:"#0F1031",strokeLinecap:"round",strokeLinejoin:"round",d:"M12.058 43.324h16.719M12 47.727h25M12 51.727h8"}),(0,ve.jsx)("rect",{width:"20",height:"15",x:"57",y:"40.727",fill:"#fff",rx:".914"}),(0,ve.jsx)("rect",{width:"20",height:"15",x:"57",y:"40.727",stroke:"#C3C4C7",strokeWidth:".914",rx:".914"}),(0,ve.jsx)("path",{fill:"#C3C4C7",d:"m61.852 47.708-4.39 4.898v3.654l14.581.003-8.615-8.624c-.404-.406-1.182-.372-1.576.069Z"}),(0,ve.jsx)("path",{fill:"#C3C4C7",d:"m72.049 49.535-3.214 3.116-3.3 3.267h11v-3.267l-3.214-3.116a.914.914 0 0 0-1.272 0Z"}),(0,ve.jsx)("circle",{cx:"70.905",cy:"45.289",r:"1.371",fill:"#C3C4C7"})]}),Te=()=>(0,ve.jsxs)("svg",{width:"89",height:"60",fill:"none",xmlns:"http://www.w3.org/2000/svg",alt:"a thumbnail representing the calendar month view",children:[(0,ve.jsx)("rect",{x:"1",y:".5",width:"87",height:"59",rx:"4.495",fill:"#F6F7F7"}),(0,ve.jsx)("rect",{x:"1",y:".5",width:"87",height:"59",rx:"4.495",stroke:"#DCDCDE"}),(0,ve.jsx)("rect",{x:"5.039",y:"5.523",width:"78.923",height:"48.952",rx:"1.5",fill:"#fff"}),(0,ve.jsx)("rect",{x:"5.039",y:"5.523",width:"78.923",height:"48.952",rx:"1.5",stroke:"#C3C4C7"}),(0,ve.jsx)("path",{d:"M82.954 30.832H5.204",stroke:"#C3C4C7",strokeLinecap:"square",strokeLinejoin:"round"}),(0,ve.jsx)("path",{stroke:"#C3C4C7",d:"M57.488 5.023v49.952M30.847 5.023v49.952"}),(0,ve.jsx)("path",{d:"M13.297 14.428h-2.664v-.576l2.61-4.554h1.188v4.086h.666v1.044h-.666v1.17h-1.134v-1.17Zm0-1.044V10.98l-1.287 2.403h1.287ZM13.594 35.017h-2.79v-1.044h4.149v.468l-2.71 5.832H11.11l2.484-5.256ZM39.395 15.67c-.354 0-.678-.07-.972-.207a1.905 1.905 0 0 1-.72-.603 1.948 1.948 0 0 1-.324-.91l1.071-.197c.114.582.43.873.945.873.306 0 .537-.096.693-.288.162-.192.243-.468.243-.828 0-.354-.087-.624-.26-.81-.169-.186-.412-.28-.73-.28-.204 0-.402.052-.594.154-.192.102-.366.24-.522.414l-.71-.144.35-3.546h3.24v1.044h-2.313l-.144 1.314c.264-.156.561-.234.891-.234.636 0 1.116.189 1.44.567.324.372.486.879.486 1.52 0 .42-.087.796-.26 1.126a1.882 1.882 0 0 1-.72.765 2.14 2.14 0 0 1-1.09.27ZM65.955 15.67c-.39 0-.747-.087-1.071-.261a2.028 2.028 0 0 1-.756-.738 2.088 2.088 0 0 1-.198-1.62c.06-.198.135-.378.225-.54l1.791-3.213h1.26l-1.323 2.268h.144c.384 0 .729.087 1.035.26.312.175.555.418.729.73.18.306.27.66.27 1.062a2 2 0 0 1-.279 1.044c-.18.312-.432.558-.756.738-.318.18-.675.27-1.071.27Zm0-1.044a.94.94 0 0 0 .693-.28c.186-.185.279-.428.279-.728 0-.312-.093-.558-.279-.738a.938.938 0 0 0-.693-.27.969.969 0 0 0-.702.27c-.18.18-.27.426-.27.738 0 .3.093.543.279.729a.96.96 0 0 0 .693.279ZM39.314 40.345c-.372 0-.714-.078-1.026-.234a1.908 1.908 0 0 1-.747-.684 1.895 1.895 0 0 1-.279-1.035c0-.348.072-.648.216-.9.144-.258.348-.462.612-.612a1.543 1.543 0 0 1-.432-.549 1.644 1.644 0 0 1-.153-.71 1.67 1.67 0 0 1 .873-1.486c.276-.156.588-.234.936-.234.348 0 .66.078.936.234.276.156.49.366.64.63.155.258.233.543.233.855 0 .258-.05.495-.153.711a1.503 1.503 0 0 1-.44.55c.263.15.467.353.611.611.15.252.225.552.225.9 0 .396-.093.741-.279 1.035a1.908 1.908 0 0 1-.747.684 2.262 2.262 0 0 1-1.026.234Zm0-3.906a.725.725 0 0 0 .55-.216.83.83 0 0 0 .206-.585.785.785 0 0 0-.207-.567.725.725 0 0 0-.549-.216.725.725 0 0 0-.549.216.785.785 0 0 0-.207.567c0 .24.07.435.207.585a.725.725 0 0 0 .55.216Zm0 2.862a.927.927 0 0 0 .657-.243c.174-.162.261-.384.261-.666 0-.282-.087-.504-.26-.666a.927.927 0 0 0-.658-.243.927.927 0 0 0-.657.243c-.174.162-.26.384-.26.666 0 .282.086.504.26.666a.927.927 0 0 0 .657.243ZM65.883 38.005c-.384 0-.732-.087-1.044-.26a1.952 1.952 0 0 1-.729-.72 2.161 2.161 0 0 1-.261-1.072c0-.384.09-.732.27-1.044.186-.312.438-.558.756-.738.324-.18.684-.27 1.08-.27.39 0 .744.09 1.062.27.324.174.579.42.765.738a2 2 0 0 1 .189 1.62 2.46 2.46 0 0 1-.216.531l-1.791 3.213h-1.26l1.323-2.268h-.144Zm.072-1.044c.282 0 .513-.09.693-.27.186-.18.279-.426.279-.738 0-.3-.096-.543-.288-.729a.93.93 0 0 0-.684-.279.94.94 0 0 0-.693.28c-.186.185-.279.428-.279.728 0 .312.09.558.27.738.186.18.42.27.702.27Z",fill:"#3C434A"})]}),Me=()=>(0,ve.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 89 60",alt:"a thumbnail representing the calendar list view",children:[(0,ve.jsx)("rect",{width:"87",height:"59",x:"1",y:".5",fill:"#F6F7F7",rx:"4.495"}),(0,ve.jsx)("rect",{width:"87",height:"59",x:"1",y:".5",stroke:"#DCDCDE",rx:"4.495"}),(0,ve.jsx)("path",{fill:"#0F1031",d:"M12.108 20.41h1.864v-4.784l-1.864 1.114v-1.595l2.697-1.583h.645v6.847h1.864v1.36h-5.206v-1.36Zm10.214-3.014c.234-.305.398-.567.492-.786.094-.226.14-.46.14-.703 0-.336-.085-.598-.257-.786-.172-.195-.422-.293-.75-.293-.297 0-.54.094-.727.282-.18.187-.293.453-.34.797l-1.36-.258c.023-.414.144-.785.363-1.114.219-.328.512-.586.88-.774a2.556 2.556 0 0 1 1.219-.293c.766 0 1.364.219 1.794.657.437.438.656 1.012.656 1.723 0 .68-.234 1.33-.703 1.947l-2.005 2.614h2.884v1.36H19.39v-.586l2.932-3.787Z"}),(0,ve.jsx)("path",{stroke:"#0F1031",strokeLinecap:"round",strokeLinejoin:"round",d:"M29.806 14.098h25.57M29.717 18.5h13.318M29.717 22.5h8"}),(0,ve.jsx)("rect",{width:"20",height:"15",x:"62",y:"10.5",fill:"#fff",rx:".914"}),(0,ve.jsx)("rect",{width:"20",height:"15",x:"62",y:"10.5",stroke:"#C3C4C7",strokeWidth:".914",rx:".914"}),(0,ve.jsx)("path",{fill:"#C3C4C7",d:"m66.852 17.481-4.39 4.898v3.654l14.581.003-8.615-8.624c-.404-.405-1.182-.371-1.576.069Z"}),(0,ve.jsx)("path",{fill:"#C3C4C7",d:"m77.049 19.308-3.214 3.117-3.3 3.266h11v-3.266l-3.214-3.117a.914.914 0 0 0-1.272 0Z"}),(0,ve.jsx)("circle",{cx:"75.905",cy:"15.062",r:"1.371",fill:"#C3C4C7"}),(0,ve.jsx)("path",{fill:"#0F1031",d:"M12.108 44.413h1.864V39.63l-1.864 1.113V39.15l2.697-1.583h.645v6.847h1.864v1.36h-5.206v-1.36Zm10.448-.164h-3.47v-.75l3.4-5.933h1.548v5.323h.867v1.36h-.867v1.524h-1.478V44.25Zm0-1.36v-3.13l-1.676 3.13h1.676Z"}),(0,ve.jsx)("path",{stroke:"#0F1031",strokeLinecap:"round",strokeLinejoin:"round",d:"M29.778 38.004h17.703M29.717 42.406l23.818.192M29.717 46.598h8"}),(0,ve.jsx)("rect",{width:"20",height:"15",x:"62",y:"34.5",fill:"#fff",rx:".914"}),(0,ve.jsx)("rect",{width:"20",height:"15",x:"62",y:"34.5",stroke:"#C3C4C7",strokeWidth:".914",rx:".914"}),(0,ve.jsx)("path",{fill:"#C3C4C7",d:"m66.852 41.481-4.39 4.898v3.654l14.581.003-8.615-8.624c-.404-.405-1.182-.371-1.576.069Z"}),(0,ve.jsx)("path",{fill:"#C3C4C7",d:"m77.049 43.308-3.214 3.117-3.3 3.266h11v-3.266l-3.214-3.117a.914.914 0 0 0-1.272 0Z"}),(0,ve.jsx)("circle",{cx:"75.905",cy:"39.062",r:"1.371",fill:"#C3C4C7"})]}),Ne=()=>(0,ve.jsxs)("svg",{viewBox:"0 0 89 60",fill:"none",xmlns:"http://www.w3.org/2000/svg",alt:"a thumbnail representing the calendar photo view",children:[(0,ve.jsx)("rect",{x:"1",y:".5",width:"87",height:"59",rx:"4.495",fill:"#F6F7F7"}),(0,ve.jsx)("rect",{x:"1",y:".5",width:"87",height:"59",rx:"4.495",stroke:"#DCDCDE"}),(0,ve.jsx)("rect",{x:"7.666",y:"12.5",width:"30.669",height:"23.001",rx:"1.401",fill:"#fff"}),(0,ve.jsx)("rect",{x:"7.666",y:"12.5",width:"30.669",height:"23.001",rx:"1.401",stroke:"#C3C4C7",strokeWidth:"1.401"}),(0,ve.jsx)("path",{d:"m14.885 22.168-6.732 7.51v5.603l22.36.005-13.21-13.224c-.621-.622-1.813-.57-2.418.106Z",fill:"#C3C4C7"}),(0,ve.jsx)("path",{d:"m30.742 26.005-4.929 4.778-5.06 5.01h16.868v-5.01l-4.928-4.779a1.401 1.401 0 0 0-1.951 0Z",fill:"#C3C4C7"}),(0,ve.jsx)("circle",{cx:"28.989",cy:"19.496",r:"2.102",fill:"#C3C4C7"}),(0,ve.jsx)("path",{d:"M8.65 45.572h1.273v-3.264l-1.272.76V41.98l1.84-1.08h.44v4.672h1.272v.928H8.65v-.928Zm6.47-2.056c.16-.208.272-.387.336-.536.064-.155.096-.315.096-.48 0-.23-.059-.408-.176-.536-.118-.133-.288-.2-.512-.2a.671.671 0 0 0-.496.192c-.123.128-.2.31-.232.544l-.928-.176c.016-.283.098-.536.248-.76.15-.224.35-.4.6-.528.25-.133.528-.2.832-.2.522 0 .93.15 1.224.448.298.299.448.69.448 1.176 0 .464-.16.907-.48 1.328l-1.368 1.784h1.968v.928h-3.56v-.4l2-2.584Z",fill:"#0F1031"}),(0,ve.jsx)("path",{d:"M19.287 41.879h18M19.316 44.52h4.71",stroke:"#0F1031",strokeLinecap:"round",strokeLinejoin:"round"}),(0,ve.jsx)("rect",{x:"50.666",y:"12.5",width:"30.669",height:"23.001",rx:"1.401",fill:"#fff"}),(0,ve.jsx)("rect",{x:"50.666",y:"12.5",width:"30.669",height:"23.001",rx:"1.401",stroke:"#C3C4C7",strokeWidth:"1.401"}),(0,ve.jsx)("path",{d:"m57.885 22.168-6.732 7.51v5.603l22.36.005-13.21-13.224c-.621-.622-1.813-.57-2.418.106Z",fill:"#C3C4C7"}),(0,ve.jsx)("path",{d:"m73.742 26.005-4.929 4.778-5.06 5.01h16.868v-5.01l-4.928-4.779a1.401 1.401 0 0 0-1.951 0Z",fill:"#C3C4C7"}),(0,ve.jsx)("circle",{cx:"71.989",cy:"19.496",r:"2.102",fill:"#C3C4C7"}),(0,ve.jsx)("path",{d:"M52.15 45.572h1.273v-3.264l-1.272.76V41.98l1.84-1.08h.44v4.672h1.272v.928H52.15v-.928Zm6.63-.112h-2.368v-.512l2.32-4.048h1.056v3.632h.592v.928h-.592v1.04H58.78v-1.04Zm0-.928v-2.136l-1.144 2.136h1.144Z",fill:"#0F1031"}),(0,ve.jsx)("path",{d:"M63.338 41.785H78.09M63.354 45.067h10.653",stroke:"#0F1031",strokeLinecap:"round",strokeLinejoin:"round"})]}),Ae=e=>(0,ve.jsxs)("svg",{viewBox:"0 0 88 60",fill:"none",xmlns:"http://www.w3.org/2000/svg",...e,children:[(0,ve.jsxs)("g",{clipPath:"url(#clip0_417_8424)",children:[(0,ve.jsx)("rect",{width:88,height:60,rx:4.99518,fill:"#F6F7F7"}),(0,ve.jsx)("path",{d:"M16.1256 19.8062C15.795 19.8062 15.491 19.7369 15.2136 19.5982C14.9363 19.4542 14.715 19.2515 14.5496 18.9902C14.3843 18.7289 14.3016 18.4222 14.3016 18.0702C14.3016 17.7609 14.3656 17.4942 14.4936 17.2702C14.6216 17.0409 14.803 16.8595 15.0376 16.7262C14.8723 16.5929 14.7443 16.4302 14.6536 16.2382C14.563 16.0462 14.5176 15.8355 14.5176 15.6062C14.5176 15.3289 14.5843 15.0755 14.7176 14.8462C14.8563 14.6115 15.0483 14.4249 15.2936 14.2862C15.539 14.1475 15.8163 14.0782 16.1256 14.0782C16.435 14.0782 16.7123 14.1475 16.9576 14.2862C17.203 14.4249 17.3923 14.6115 17.5256 14.8462C17.6643 15.0755 17.7336 15.3289 17.7336 15.6062C17.7336 15.8355 17.6883 16.0462 17.5976 16.2382C17.507 16.4302 17.3763 16.5929 17.2056 16.7262C17.4403 16.8595 17.6216 17.0409 17.7496 17.2702C17.883 17.4942 17.9496 17.7609 17.9496 18.0702C17.9496 18.4222 17.867 18.7289 17.7016 18.9902C17.5363 19.2515 17.315 19.4542 17.0376 19.5982C16.7603 19.7369 16.4563 19.8062 16.1256 19.8062ZM16.1256 16.3342C16.3283 16.3342 16.491 16.2702 16.6136 16.1422C16.7363 16.0089 16.7976 15.8355 16.7976 15.6222C16.7976 15.4142 16.7363 15.2462 16.6136 15.1182C16.491 14.9902 16.3283 14.9262 16.1256 14.9262C15.923 14.9262 15.7603 14.9902 15.6376 15.1182C15.515 15.2462 15.4536 15.4142 15.4536 15.6222C15.4536 15.8355 15.515 16.0089 15.6376 16.1422C15.7603 16.2702 15.923 16.3342 16.1256 16.3342ZM16.1256 18.8782C16.3603 18.8782 16.555 18.8062 16.7096 18.6622C16.8643 18.5182 16.9416 18.3209 16.9416 18.0702C16.9416 17.8195 16.8643 17.6222 16.7096 17.4782C16.555 17.3342 16.3603 17.2622 16.1256 17.2622C15.891 17.2622 15.6963 17.3342 15.5416 17.4782C15.387 17.6222 15.3096 17.8195 15.3096 18.0702C15.3096 18.3209 15.387 18.5182 15.5416 18.6622C15.6963 18.8062 15.891 18.8782 16.1256 18.8782Z",fill:"#0F1031"}),(0,ve.jsx)("path",{d:"M21.6779 15.0272H42.3306",stroke:"#0F1031",strokeLinecap:"round",strokeLinejoin:"round"}),(0,ve.jsx)("path",{d:"M21.655 18.3088H29.4031",stroke:"#0F1031",strokeLinecap:"round",strokeLinejoin:"round"}),(0,ve.jsx)("path",{d:"M9.09163 31.8142H10.3636V28.5502L9.09163 29.3102V28.2222L10.9316 27.1422H11.3716V31.8142H12.6436V32.7422H9.09163V31.8142ZM15.5606 29.7582C15.7206 29.5502 15.8326 29.3715 15.8966 29.2222C15.9606 29.0675 15.9926 28.9075 15.9926 28.7422C15.9926 28.5129 15.9339 28.3342 15.8166 28.2062C15.6992 28.0729 15.5286 28.0062 15.3046 28.0062C15.1019 28.0062 14.9366 28.0702 14.8086 28.1982C14.6859 28.3262 14.6086 28.5075 14.5766 28.7422L13.6486 28.5662C13.6646 28.2835 13.7472 28.0302 13.8966 27.8062C14.0459 27.5822 14.2459 27.4062 14.4966 27.2782C14.7472 27.1449 15.0246 27.0782 15.3286 27.0782C15.8512 27.0782 16.2592 27.2275 16.5526 27.5262C16.8512 27.8249 17.0006 28.2169 17.0006 28.7022C17.0006 29.1662 16.8406 29.6089 16.5206 30.0302L15.1526 31.8142H17.1206V32.7422H13.5606V32.3422L15.5606 29.7582Z",fill:"#0F1031"}),(0,ve.jsx)("path",{d:"M21.6575 28.0272H36.4094",stroke:"#0F1031",strokeLinecap:"round",strokeLinejoin:"round"}),(0,ve.jsx)("path",{d:"M21.6731 31.3088H32.3268",stroke:"#0F1031",strokeLinecap:"round",strokeLinejoin:"round"}),(0,ve.jsx)("path",{d:"M9.09163 44.8142H10.3636V41.5502L9.09163 42.3102V41.2222L10.9316 40.1422H11.3716V44.8142H12.6436V45.7422H9.09163V44.8142ZM15.7206 44.7022H13.3526V44.1902L15.6726 40.1422H16.7286V43.7742H17.3206V44.7022H16.7286V45.7422H15.7206V44.7022ZM15.7206 43.7742V41.6382L14.5766 43.7742H15.7206Z",fill:"#0F1031"}),(0,ve.jsx)("path",{d:"M21.6066 40.9297H40.1066",stroke:"#0F1031",strokeLinecap:"round",strokeLinejoin:"round"}),(0,ve.jsx)("path",{d:"M21.6308 43.8088H25.5048",stroke:"#0F1031",strokeLinecap:"round",strokeLinejoin:"round"}),(0,ve.jsxs)("g",{clipPath:"url(#clip1_417_8424)",children:[(0,ve.jsx)("rect",{x:47.5787,y:12.75,width:31.5745,height:34.9574,rx:2.6293,fill:"white"}),(0,ve.jsxs)("g",{clipPath:"url(#clip2_417_8424)",children:[(0,ve.jsx)("mask",{id:"mask0_417_8424",style:{maskType:"luminance"},maskUnits:"userSpaceOnUse",x:47,y:12,width:106,height:107,children:(0,ve.jsx)("path",{d:"M152.292 12.8828H47.3253V118.191H152.292V12.8828Z",fill:"white"})}),(0,ve.jsxs)("g",{mask:"url(#mask0_417_8424)",children:[(0,ve.jsx)("path",{d:"M52.3661 18.4155L54.3983 18.1733L57.3604 15.0625",stroke:"#A0A09C",strokeWidth:.438217,strokeLinecap:"round",strokeLinejoin:"round"}),(0,ve.jsx)("path",{d:"M55.7986 13.9532L59.4032 16.5164L68.4303 11.4258",stroke:"#A0A09C",strokeWidth:.438217,strokeLinecap:"round",strokeLinejoin:"round"}),(0,ve.jsx)("path",{d:"M57.8622 17.1813L60.4864 18.6915L63.077 17.1813L62.9006 15.707",stroke:"#A0A09C",strokeWidth:.438217,strokeLinecap:"round",strokeLinejoin:"round"}),(0,ve.jsx)("path",{d:"M65.0356 14.3359L59.2225 17.9627",stroke:"#A0A09C",strokeWidth:.438217,strokeLinecap:"round",strokeLinejoin:"round"}),(0,ve.jsx)("path",{d:"M61.7353 17.9641L60.9418 16.7383",stroke:"#A0A09C",strokeWidth:.438217,strokeLinecap:"round",strokeLinejoin:"round"}),(0,ve.jsx)("path",{d:"M66.6451 12.8867L68.5513 15.8775",stroke:"#A0A09C",strokeWidth:.438217,strokeLinecap:"round",strokeLinejoin:"round"}),(0,ve.jsx)("path",{d:"M53.4551 19.5703L51.6686 22.4852L47.8898 23.8542",stroke:"#A0A09C",strokeWidth:.438217,strokeLinecap:"round",strokeLinejoin:"round"}),(0,ve.jsx)("path",{d:"M52.9767 24.9826L51.6688 22.4805",stroke:"#A0A09C",strokeWidth:.438217,strokeLinecap:"round",strokeLinejoin:"round"}),(0,ve.jsx)("path",{d:"M63.339 19.0117C63.339 19.0117 67.9743 20.5239 69.4082 21.0273C70.842 21.5307 73.7979 23.1566 73.7979 23.1566C73.7979 23.1566 77.3941 25.4945 79.1009 25.6335C80.8076 25.7746 83.604 25.6314 85.254 26.061C86.902 26.4928 89.9671 27.5101 89.9671 27.5101L97.1111 29.5783L107.637 32.6112C107.637 32.6112 117.876 34.2603 118.953 34.1887C120.028 34.1171 121.176 33.6916 122.538 33.759C123.901 33.8285 128.496 34.1634 129.676 34.6436C130.856 35.1238 135.015 37.2805 136.161 37.3521C137.307 37.4237 142.52 36.5602 142.52 36.5602C142.52 36.5602 145.052 36.2001 145.984 36.5602C146.916 36.9204 153.212 37.3563 153.212 37.3563",stroke:"#A0A09C",strokeWidth:.438217,strokeLinecap:"round",strokeLinejoin:"round"}),(0,ve.jsx)("path",{d:"M57.8622 23.7658C57.8622 23.7658 59.8943 23.2771 60.9419 22.9149C61.9895 22.5526 62.0546 22.5779 63.3394 22.481C64.6242 22.3862 65.7893 22.1882 67.177 20.2695",stroke:"#A0A09C",strokeWidth:.438217,strokeLinecap:"round",strokeLinejoin:"round"}),(0,ve.jsx)("path",{d:"M61.3387 20.1484L60.4696 23.0676",stroke:"#A0A09C",strokeWidth:.438217,strokeLinecap:"round",strokeLinejoin:"round"}),(0,ve.jsx)("path",{d:"M63.6918 19.125L61.7352 22.6654",stroke:"#A0A09C",strokeWidth:.438217,strokeLinecap:"round",strokeLinejoin:"round"}),(0,ve.jsx)("path",{d:"M64.4006 19.3594L62.8744 22.5186",stroke:"#A0A09C",strokeWidth:.438217,strokeLinecap:"round",strokeLinejoin:"round"}),(0,ve.jsx)("path",{d:"M65.7035 19.7852L64.3053 22.3631",stroke:"#A0A09C",strokeWidth:.438217,strokeLinecap:"round",strokeLinejoin:"round"}),(0,ve.jsx)("path",{d:"M67.7312 21.7109L64.1896 28.8403",stroke:"#A0A09C",strokeWidth:.438217,strokeLinecap:"round",strokeLinejoin:"round"}),(0,ve.jsx)("path",{d:"M70.6702 21.5586L66.4967 30.752",stroke:"#A0A09C",strokeWidth:.438217,strokeLinecap:"round",strokeLinejoin:"round"}),(0,ve.jsx)("path",{d:"M66.4967 24.1992L68.9823 25.2755",stroke:"#A0A09C",strokeWidth:.438217,strokeLinecap:"round",strokeLinejoin:"round"}),(0,ve.jsx)("path",{d:"M65.3317 29.7825L67.7312 24.7383",stroke:"#A0A09C",strokeWidth:.438217,strokeLinecap:"round",strokeLinejoin:"round"}),(0,ve.jsx)("path",{d:"M65.4093 26.3867L67.9033 27.6504",stroke:"#A0A09C",strokeWidth:.438217,strokeLinecap:"round",strokeLinejoin:"round"}),(0,ve.jsx)("path",{d:"M71.854 22.1289L67.3615 31.4697",stroke:"#A0A09C",strokeWidth:.438217,strokeLinecap:"round",strokeLinejoin:"round"}),(0,ve.jsx)("path",{d:"M66.1599 24.8703L65.2068 24.5312L62.8744 27.76",stroke:"#A0A09C",strokeWidth:.438217,strokeLinecap:"round",strokeLinejoin:"round"}),(0,ve.jsx)("path",{d:"M68.1069 16.1523L68.5541 20.7332C68.5541 20.7332 71.722 17.9363 76.5799 17.5045C81.4378 17.0727 83.7659 17.5572 86.1382 16.9127",stroke:"#A0A09C",strokeWidth:.438217,strokeLinecap:"round",strokeLinejoin:"round"}),(0,ve.jsx)("path",{d:"M68.4594 19.7851L70.8631 18.4414",stroke:"#A0A09C",strokeWidth:.438217,strokeLinecap:"round",strokeLinejoin:"round"}),(0,ve.jsx)("path",{d:"M75.3018 20.1486L73.3893 19.125",stroke:"#A0A09C",strokeWidth:.438217,strokeLinecap:"round",strokeLinejoin:"round"}),(0,ve.jsx)("path",{d:"M74.0252 17.9688L72.6942 20.3908",stroke:"#A0A09C",strokeWidth:.438217,strokeLinecap:"round",strokeLinejoin:"round"}),(0,ve.jsx)("path",{d:"M67.1768 22.828L66.6457 22.668L63.0768 25.7556",stroke:"#A0A09C",strokeWidth:.438217,strokeLinecap:"round",strokeLinejoin:"round"}),(0,ve.jsx)("path",{d:"M63.637 23.8516L59.4991 25.0458",stroke:"#A0A09C",strokeWidth:.438217,strokeLinecap:"round",strokeLinejoin:"round"}),(0,ve.jsx)("path",{d:"M82.0522 11.9531L70.1993 33.8593",stroke:"#A0A09C",strokeWidth:.438217,strokeLinecap:"round",strokeLinejoin:"round"}),(0,ve.jsx)("path",{d:"M86.4492 11.9531L72.9672 36.2161L64.1962 45.7527",stroke:"#A0A09C",strokeWidth:.438217,strokeLinecap:"round",strokeLinejoin:"round"}),(0,ve.jsx)("path",{d:"M83.7893 16.7383L81.0958 17.2964L71.6635 35.1019",stroke:"#A0A09C",strokeWidth:.438217,strokeLinecap:"round",strokeLinejoin:"round"}),(0,ve.jsx)("path",{d:"M76.5433 22.1289L78.1241 22.904",stroke:"#A0A09C",strokeWidth:.438217,strokeLinecap:"round",strokeLinejoin:"round"}),(0,ve.jsx)("path",{d:"M100.54 43.383L94.2273 38.2461L92.5478 39.5709L90.0223 44.6383L92.6633 48.3388L82.0259 58.7369L70.4123 62.6775L64.8407 64.969L61.7987 67.0731L63.9022 69.9459",stroke:"#A0A09C",strokeWidth:.438217,strokeLinecap:"round",strokeLinejoin:"round"}),(0,ve.jsx)("path",{d:"M47.3251 72.9798L51.3411 68.0556L52.5021 66.3032L54.8491 62.866L64.4746 54.0938L78.8845 41.4L89.9669 27.5078",stroke:"#A0A09C",strokeWidth:.438217,strokeLinecap:"round",strokeLinejoin:"round"}),(0,ve.jsx)("path",{d:"M77.7257 31.4727L88.1741 36.2157L92.5471 39.5708",stroke:"#A0A09C",strokeWidth:.438217,strokeLinecap:"round",strokeLinejoin:"round"}),(0,ve.jsx)("path",{d:"M74.6674 67.0016L72.7864 61.2033L67.0405 52.1995L65.1259 50.5946L68.552 46.6203L74.0838 37.1742L77.3356 31.6371L80.218 25.6914",stroke:"#A0A09C",strokeWidth:.438217,strokeLinecap:"round",strokeLinejoin:"round"}),(0,ve.jsx)("path",{d:"M79.399 32.2305L75.3199 38.2457",stroke:"#A0A09C",strokeWidth:.438217,strokeLinecap:"round",strokeLinejoin:"round"}),(0,ve.jsx)("path",{d:"M81.1686 33.0352L76.4955 39.2736L71.6691 44.8086",stroke:"#A0A09C",strokeWidth:.438217,strokeLinecap:"round",strokeLinejoin:"round"}),(0,ve.jsx)("path",{d:"M83.4337 34.0625L78.0258 40.6274",stroke:"#A0A09C",strokeWidth:.438217,strokeLinecap:"round",strokeLinejoin:"round"}),(0,ve.jsx)("path",{d:"M70.7103 38.6719L72.2911 40.241",stroke:"#A0A09C",strokeWidth:.438217,strokeLinecap:"round",strokeLinejoin:"round"}),(0,ve.jsx)("path",{d:"M65.6647 37.4805L77.358 47.4489L84.9115 55.9136L89.1437 62.4511",stroke:"#A0A09C",strokeWidth:.438217,strokeLinecap:"round",strokeLinejoin:"round"}),(0,ve.jsx)("path",{d:"M83.4332 57.3628L78.0253 51.5014L72.6699 46.8784L69.8169 44.4668L64.2977 39.5742",stroke:"#A0A09C",strokeWidth:.438217,strokeLinecap:"round",strokeLinejoin:"round"}),(0,ve.jsx)("path",{d:"M62.943 41.543L68.5524 46.6251L76.1269 53.805L80.4515 59.2726",stroke:"#A0A09C",strokeWidth:.438217,strokeLinecap:"round",strokeLinejoin:"round"}),(0,ve.jsx)("path",{d:"M67.2123 48.1807L61.6742 43.3008",stroke:"#A0A09C",strokeWidth:.438217,strokeLinecap:"round",strokeLinejoin:"round"}),(0,ve.jsx)("path",{d:"M83.1902 45.6914L79.2854 49.611L78.0258 51.4981L76.1259 53.8043L74.2302 56.258L70.0231 59.2719",stroke:"#A0A09C",strokeWidth:.438217,strokeLinecap:"round",strokeLinejoin:"round"}),(0,ve.jsx)("path",{d:"M77.3578 47.447L81.0085 43.418",stroke:"#A0A09C",strokeWidth:.438217,strokeLinecap:"round",strokeLinejoin:"round"}),(0,ve.jsx)("path",{d:"M66.6559 64.2234L61.3803 56.915L58.6092 52.7111L54.6456 49.5224L50.1173 46.2305",stroke:"#A0A09C",strokeWidth:.438217,strokeLinecap:"round",strokeLinejoin:"round"}),(0,ve.jsx)("path",{d:"M46.2456 59.1026L51.3071 55.6274L58.3609 47.4492",stroke:"#A0A09C",strokeWidth:.438217,strokeLinecap:"round",strokeLinejoin:"round"}),(0,ve.jsx)("path",{d:"M52.3233 46.3516L46.2457 51.7939",stroke:"#A0A09C",strokeWidth:.438217,strokeLinecap:"round",strokeLinejoin:"round"}),(0,ve.jsx)("path",{d:"M51.4542 46.2465L50.1169 44.8059L46.2457 43.418",stroke:"#A0A09C",strokeWidth:.438217,strokeLinecap:"round",strokeLinejoin:"round"}),(0,ve.jsx)("path",{d:"M46.2457 44.6043L52.5962 36.5819L56.501 33.0352",stroke:"#A0A09C",strokeWidth:.438217,strokeLinecap:"round",strokeLinejoin:"round"}),(0,ve.jsx)("path",{d:"M61.5671 26.6992L58.0045 31.0484L60.9415 34.3446L65.77 30.147",stroke:"#A0A09C",strokeWidth:.438217,strokeLinecap:"round",strokeLinejoin:"round"}),(0,ve.jsx)("path",{d:"M67.6131 34.3415L62.5201 29.7227",stroke:"#A0A09C",strokeWidth:.438217,strokeLinecap:"round",strokeLinejoin:"round"}),(0,ve.jsx)("path",{d:"M59.5863 29.1172L61.5681 31.6404L65.771 31.9078",stroke:"#A0A09C",strokeWidth:.438217,strokeLinecap:"round",strokeLinejoin:"round"}),(0,ve.jsx)("path",{d:"M58.5653 25.6328L56.8334 27.3662L58.5653 29.1185L60.3435 27.2588L58.5653 25.6328Z",stroke:"#A0A09C",strokeWidth:.438217,strokeLinecap:"round",strokeLinejoin:"round"}),(0,ve.jsx)("path",{d:"M46.2457 21.2164L50.1862 18.6953",stroke:"#A0A09C",strokeWidth:.438217,strokeLinecap:"round",strokeLinejoin:"round"}),(0,ve.jsx)("path",{d:"M61.886 43.0092L56.5011 39.6836L50.7238 46.2295",stroke:"#A0A09C",strokeWidth:.438217,strokeLinecap:"round",strokeLinejoin:"round"}),(0,ve.jsx)("path",{d:"M48.9603 41.1719L54.9036 46.73",stroke:"#A0A09C",strokeWidth:.438217,strokeLinecap:"round",strokeLinejoin:"round"}),(0,ve.jsx)("path",{d:"M58.3623 43.953L59.2251 42.2196L56.5023 39.6838L49.5765 34.0625",stroke:"#A0A09C",strokeWidth:.438217,strokeLinecap:"round",strokeLinejoin:"round"}),(0,ve.jsx)("path",{d:"M46.2462 11.4258C46.2462 11.4258 53.9088 20.7982 58.6323 24.3786C63.3559 27.9591 79.8357 41.8598 81.3052 43.7553C82.7748 45.6508 85.0841 47.0198 85.7139 48.1782C86.3437 49.3366 91.802 68.3973 92.9566 72.0831C94.1112 75.7689 97.6801 85.7731 97.6801 85.7731C97.6801 85.7731 98.7298 88.3005 100.304 87.8793C100.304 87.8793 102.185 87.774 101.035 84.0882C99.8844 80.4024 97.7452 72.0305 101.805 61.0511C101.805 61.0511 107.398 72.5043 109.151 74.6105C110.904 76.7167 115.942 84.7201 117.307 85.7731C118.671 86.8262 119.511 88.8271 120.666 90.1961C121.82 91.5651 122.135 93.3553 124.235 95.0402C126.334 96.7252 127.804 101.043 126.859 106.308C125.914 111.574 129.168 119.366 129.168 119.366",stroke:"#686866",strokeWidth:.438217,strokeLinecap:"round",strokeLinejoin:"round"}),(0,ve.jsx)("path",{d:"M79.1008 11.9531C79.1008 11.9531 69.2843 35.0935 58.0045 47.861",stroke:"#686866",strokeWidth:.438217,strokeLinecap:"round",strokeLinejoin:"round"}),(0,ve.jsx)("path",{d:"M46.2462 53.3375C46.2462 53.3375 54.4358 49.7528 58.0046 47.8594",stroke:"#686866",strokeWidth:.438217,strokeLinecap:"round",strokeLinejoin:"round"}),(0,ve.jsx)("path",{d:"M46.2462 46.7043C46.2462 46.7043 56.638 43.9663 62.6211 51.6538C68.6042 59.3412 73.3529 67.3825 73.3529 67.3825C73.3529 67.3825 79.3109 85.0364 80.7804 88.6168C82.25 92.1973 84.2443 93.9875 83.0897 96.7255C81.9351 99.4635 80.5705 105.887 80.5705 105.887",stroke:"#222221",strokeWidth:.438217,strokeLinecap:"round",strokeLinejoin:"round"}),(0,ve.jsx)("path",{d:"M46.2462 29.1175C46.2462 29.1175 55.4833 23.3256 60.7317 20.4823C65.98 17.6389 75.637 11.4258 75.637 11.4258",stroke:"#222221",strokeWidth:.438217,strokeLinecap:"round",strokeLinejoin:"round"}),(0,ve.jsx)("path",{d:"M134.418 17.5034C134.418 17.5034 124.971 17.7435 121.507 17.5329C118.044 17.3223 116.469 18.2701 114.475 19.2178C112.48 20.1656 98.8346 24.6938 92.8514 26.5894C86.8683 28.4849 70.7244 34.3042 70.7244 34.3042L56.7112 22.7625L47.3251 12.8867",stroke:"#222221",strokeWidth:.438217,strokeLinecap:"round",strokeLinejoin:"round"})]})]})]}),(0,ve.jsx)("rect",{x:47.9074,y:13.0787,width:30.9171,height:34.3001,rx:2.30064,stroke:"#2C3338",strokeWidth:.657325})]}),(0,ve.jsx)("rect",{x:.5,y:.5,width:87,height:59,rx:4.49518,stroke:"#DCDCDE"}),(0,ve.jsxs)("defs",{children:[(0,ve.jsx)("clipPath",{id:"clip0_417_8424",children:(0,ve.jsx)("rect",{width:88,height:60,rx:4.99518,fill:"white"})}),(0,ve.jsx)("clipPath",{id:"clip1_417_8424",children:(0,ve.jsx)("rect",{x:47.5787,y:12.75,width:31.5745,height:34.9574,rx:2.6293,fill:"white"})}),(0,ve.jsx)("clipPath",{id:"clip2_417_8424",children:(0,ve.jsx)("rect",{width:107.801,height:108.678,fill:"white",transform:"translate(47.5787 12.75)"})})]})]}),Ee=e=>(0,ve.jsxs)("svg",{viewBox:"0 0 88 60",fill:"none",xmlns:"http://www.w3.org/2000/svg",...e,children:[(0,ve.jsx)("rect",{x:.5,y:.5,width:87,height:59,rx:4.49518,fill:"#F6F7F7"}),(0,ve.jsx)("rect",{x:.5,y:.5,width:87,height:59,rx:4.49518,stroke:"#DCDCDE"}),(0,ve.jsx)("path",{d:"M16.7601 15.8492C16.3284 15.8492 15.9315 15.7587 15.5695 15.5776C15.2074 15.3896 14.9184 15.1251 14.7026 14.7839C14.4868 14.4427 14.3788 14.0423 14.3788 13.5828C14.3788 13.179 14.4624 12.8308 14.6295 12.5384C14.7966 12.239 15.0333 12.0022 15.3397 11.8282C15.1239 11.6541 14.9567 11.4417 14.8384 11.1911C14.72 10.9404 14.6608 10.6654 14.6608 10.366C14.6608 10.0039 14.7479 9.6732 14.9219 9.3738C15.103 9.06744 15.3536 8.82375 15.6739 8.64271C15.9942 8.46168 16.3563 8.37117 16.7601 8.37117C17.1639 8.37117 17.526 8.46168 17.8463 8.64271C18.1666 8.82375 18.4138 9.06744 18.5878 9.3738C18.7689 9.6732 18.8594 10.0039 18.8594 10.366C18.8594 10.6654 18.8002 10.9404 18.6818 11.1911C18.5635 11.4417 18.3929 11.6541 18.1701 11.8282C18.4764 12.0022 18.7132 12.239 18.8803 12.5384C19.0543 12.8308 19.1414 13.179 19.1414 13.5828C19.1414 14.0423 19.0334 14.4427 18.8176 14.7839C18.6018 15.1251 18.3128 15.3896 17.9507 15.5776C17.5887 15.7587 17.1918 15.8492 16.7601 15.8492ZM16.7601 11.3164C17.0247 11.3164 17.2371 11.2329 17.3972 11.0658C17.5573 10.8917 17.6374 10.6654 17.6374 10.3869C17.6374 10.1153 17.5573 9.89601 17.3972 9.72891C17.2371 9.5618 17.0247 9.47825 16.7601 9.47825C16.4955 9.47825 16.2832 9.5618 16.123 9.72891C15.9629 9.89601 15.8828 10.1153 15.8828 10.3869C15.8828 10.6654 15.9629 10.8917 16.123 11.0658C16.2832 11.2329 16.4955 11.3164 16.7601 11.3164ZM16.7601 14.6377C17.0665 14.6377 17.3206 14.5437 17.5225 14.3557C17.7244 14.1677 17.8254 13.91 17.8254 13.5828C17.8254 13.2555 17.7244 12.9979 17.5225 12.8099C17.3206 12.6219 17.0665 12.5279 16.7601 12.5279C16.4537 12.5279 16.1996 12.6219 15.9977 12.8099C15.7958 12.9979 15.6948 13.2555 15.6948 13.5828C15.6948 13.91 15.7958 14.1677 15.9977 14.3557C16.1996 14.5437 16.4537 14.6377 16.7601 14.6377Z",fill:"#0F1031"}),(0,ve.jsx)("path",{d:"M24.0087 9.75277H50.9711",stroke:"#0F1031",strokeWidth:1.30552,strokeLinecap:"round",strokeLinejoin:"round"}),(0,ve.jsx)("path",{d:"M23.9787 14.0369H34.094",stroke:"#0F1031",strokeWidth:1.30552,strokeLinecap:"round",strokeLinejoin:"round"}),(0,ve.jsx)("path",{d:"M7.57708 31.9994H9.2377V27.7382L7.57708 28.7304V27.31L9.97924 25.9H10.5537V31.9994H12.2143V33.2109H7.57708V31.9994ZM16.0224 29.3153C16.2313 29.0437 16.3775 28.8105 16.4611 28.6155C16.5446 28.4136 16.5864 28.2047 16.5864 27.9889C16.5864 27.6895 16.5098 27.4562 16.3566 27.2891C16.2034 27.115 15.9806 27.028 15.6882 27.028C15.4236 27.028 15.2078 27.1116 15.0407 27.2787C14.8805 27.4458 14.7795 27.6825 14.7378 27.9889L13.5262 27.7591C13.5471 27.3901 13.6551 27.0593 13.85 26.7669C14.045 26.4745 14.3061 26.2447 14.6333 26.0776C14.9606 25.9035 15.3226 25.8165 15.7195 25.8165C16.4019 25.8165 16.9345 26.0114 17.3175 26.4014C17.7074 26.7913 17.9023 27.303 17.9023 27.9366C17.9023 28.5424 17.6935 29.1203 17.2757 29.6704L15.4897 31.9994H18.059V33.2109H13.4114V32.6887L16.0224 29.3153Z",fill:"#0F1031"}),(0,ve.jsx)("path",{d:"M23.9821 27.1981H43.2409",stroke:"#0F1031",strokeWidth:1.30552,strokeLinecap:"round",strokeLinejoin:"round"}),(0,ve.jsx)("path",{d:"M24.0024 31.4822H37.911",stroke:"#0F1031",strokeWidth:1.30552,strokeLinecap:"round",strokeLinejoin:"round"}),(0,ve.jsx)("path",{d:"M7.57708 49.4408H9.2377V45.1796L7.57708 46.1718V44.7514L9.97924 43.3414H10.5537V49.4408H12.2143V50.6523H7.57708V49.4408ZM16.2313 49.2946H13.1398V48.6262L16.1686 43.3414H17.5472V48.0831H18.3201V49.2946H17.5472V50.6523H16.2313V49.2946ZM16.2313 48.0831V45.2945L14.7378 48.0831H16.2313Z",fill:"#0F1031"}),(0,ve.jsx)("path",{d:"M23.9155 44.5117H48.0676",stroke:"#0F1031",strokeWidth:1.30552,strokeLinecap:"round",strokeLinejoin:"round"}),(0,ve.jsx)("path",{d:"M23.9471 48.2712H29.0048",stroke:"#0F1031",strokeWidth:1.30552,strokeLinecap:"round",strokeLinejoin:"round"})]}),Fe=()=>(0,ve.jsxs)("svg",{viewBox:"0 0 89 60",fill:"none",xmlns:"http://www.w3.org/2000/svg",alt:"a thumbnail representing the calendar week view",children:[(0,ve.jsx)("rect",{x:"1",y:".5",width:"87",height:"59",rx:"4.495",fill:"#F6F7F7"}),(0,ve.jsx)("rect",{x:"1",y:".5",width:"87",height:"59",rx:"4.495",stroke:"#DCDCDE"}),(0,ve.jsx)("rect",{x:"5",y:"5.5",width:"79",height:"49",rx:"1.5",fill:"#fff"}),(0,ve.jsx)("rect",{x:"5",y:"5.5",width:"79",height:"49",rx:"1.5",stroke:"#C3C4C7"}),(0,ve.jsx)("path",{d:"M13.258 14.4h-2.664v-.576l2.61-4.554h1.188v4.086h.666V14.4h-.666v1.17h-1.134V14.4Zm0-1.044v-2.403l-1.287 2.403h1.287ZM39.356 15.642c-.354 0-.678-.069-.971-.207a1.905 1.905 0 0 1-.72-.603 1.948 1.948 0 0 1-.325-.909l1.071-.198c.114.582.43.873.945.873.306 0 .538-.096.694-.288.162-.192.242-.468.242-.828 0-.354-.087-.624-.26-.81-.168-.186-.411-.279-.73-.279-.203 0-.401.051-.593.153-.193.102-.367.24-.523.414l-.71-.144.35-3.546h3.24v1.044h-2.313l-.144 1.314c.264-.156.561-.234.892-.234.636 0 1.116.19 1.44.567.323.372.485.88.485 1.521 0 .42-.087.795-.26 1.125a1.882 1.882 0 0 1-.72.765 2.14 2.14 0 0 1-1.09.27ZM65.916 15.642c-.39 0-.746-.087-1.07-.26a2.028 2.028 0 0 1-.757-.739 2.088 2.088 0 0 1-.198-1.62c.06-.198.135-.378.225-.54l1.791-3.213h1.26l-1.323 2.268h.145c.383 0 .728.087 1.034.261.313.174.556.417.73.73.18.305.27.66.27 1.061a2 2 0 0 1-.28 1.044c-.18.312-.431.558-.755.738-.319.18-.675.27-1.072.27Zm0-1.044a.94.94 0 0 0 .694-.279c.186-.186.278-.429.278-.729 0-.312-.093-.558-.279-.738a.938.938 0 0 0-.692-.27.969.969 0 0 0-.703.27c-.18.18-.27.426-.27.738 0 .3.093.543.28.73a.96.96 0 0 0 .692.278Z",fill:"#3C434A"}),(0,ve.jsx)("path",{stroke:"#C3C4C7",d:"M30.46 5.773v49M58.46 5.773v49"})]}),Ve=e=>(0,ve.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 12 12",...e,children:[(0,ve.jsxs)("g",{clipPath:"url(#a)",children:[(0,ve.jsx)("path",{fill:"#FFCF48",d:"M11.879 6.072a5.927 5.927 0 1 1-11.855 0 5.927 5.927 0 0 1 11.855 0Z"}),(0,ve.jsx)("path",{fill:"#0F1031",d:"m5.675 6.118-2.073-.06 4.702-4.324L6.246 5.6l2.073.06-4.412 4.683 1.768-4.224Z"})]}),(0,ve.jsx)("defs",{children:(0,ve.jsx)("clipPath",{id:"a",children:(0,ve.jsx)("path",{fill:"#fff",d:"M0 0h12v12H0z"})})})]}),We=new Map;We.set("day",(0,ve.jsx)(ye,{})),We.set("month",(0,ve.jsx)(Te,{})),We.set("list",(0,ve.jsx)(Me,{})),We.set("map",(0,ve.jsx)(Ae,{})),We.set("photo",(0,ve.jsx)(Ne,{})),We.set("summary",(0,ve.jsx)(Ee,{})),We.set("week",(0,ve.jsx)(Fe,{}));const Ie=new Map;Ie.set("map",(0,_.__)("Map","the-events-calendar")),Ie.set("photo",(0,_.__)("Photo","the-events-calendar")),Ie.set("summary",(0,_.__)("Summary","the-events-calendar")),Ie.set("week",(0,_.__)("Week","the-events-calendar"));const Ze=({moveToNextTab:e,skipToNextTab:t})=>{const n=(0,v.useSelect)((e=>e(J).getSetting("availableViews")||[]),[]),s=(0,v.useSelect)((e=>e(J).getSetting("tribeEnableViews")||[]),[]),a=n.length>3,[i,r]=(0,u.useState)(s),o=n.every((e=>i.includes(e))),d=(e,t)=>{r("all"===e?t?[...n]:[]:n=>t?[...n,e]:n.filter((t=>t!==e)))},l={tribeEnableViews:i,currentTab:1},c=i.length>0;return(0,ve.jsxs)(ve.Fragment,{children:[(0,ve.jsxs)("div",{className:"tec-events-onboarding__tab-header",children:[(0,ve.jsx)("h1",{className:"tec-events-onboarding__tab-heading",children:(0,_.__)("How do you want people to view your calendar?","the-events-calendar")}),(0,ve.jsx)("p",{className:"tec-events-onboarding__tab-subheader",children:(0,_.__)("Select how you want to display your events on your site. You can choose more than one.","the-events-calendar")})]}),(0,ve.jsxs)("div",{className:"tec-events-onboarding__tab-content",children:[(0,ve.jsxs)("div",{className:"tec-events-onboarding__grid--view-checkbox",children:[n.map(((e,t)=>(0,ve.jsx)("span",{children:(0,ve.jsx)(Se,{view:e,isChecked:i.includes(e),onChange:d,icon:We.get(e)})},t))),(0,ve.jsx)(Se,{view:"all",isChecked:o,onChange:d,icon:""})]}),!a&&(0,ve.jsxs)("div",{className:"tec-events-onboarding__view_upsell",children:[(0,ve.jsxs)("p",{className:"tec-events-onboarding__view_upsell_callout",children:[(0,_.__)("More views available with","the-events-calendar")," ",(0,ve.jsx)(Ve,{className:"tec-events-onboarding_pro-icon"}),(0,ve.jsx)("a",{href:"https://evnt.is/ecp",target:"_blank",rel:"noopener noreferrer",children:(0,_.__)("Events Calendar Pro","the-events-calendar")})]}),(0,ve.jsxs)("div",{className:"tec-events-onboarding__view_upsell_list",children:[(0,ve.jsxs)("div",{className:"tec-events-onboarding__view_upsell-cell",children:[We.get("map"),(0,ve.jsx)("span",{className:"tec-events-onboarding__view_upsell-label",children:Ie.get("map")})]}),(0,ve.jsxs)("div",{className:"tec-events-onboarding__view_upsell-cell",children:[We.get("photo"),(0,ve.jsx)("span",{className:"tec-events-onboarding__view_upsell-label",children:Ie.get("photo")})]}),(0,ve.jsxs)("div",{className:"tec-events-onboarding__view_upsell-cell",children:[We.get("summary"),(0,ve.jsx)("span",{className:"tec-events-onboarding__view_upsell-label",children:Ie.get("summary")})]}),(0,ve.jsxs)("div",{className:"tec-events-onboarding__view_upsell-cell",children:[We.get("week"),(0,ve.jsx)("span",{className:"tec-events-onboarding__view_upsell-label",children:Ie.get("week")})]})]})]}),!c&&(0,ve.jsx)("p",{className:"tec-events-onboarding__view_required_notice",children:(0,_.__)("Please select at least one view to continue.","the-events-calendar")}),(0,ve.jsx)(fe,{disabled:!c,moveToNextTab:e,tabSettings:l}),(0,ve.jsx)(we,{skipToNextTab:t,currentTab:1})]})]})},ze=()=>(0,ve.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 31 30",className:"tec-events-onboarding__content-header-icon",role:"presentation",children:[(0,ve.jsxs)("g",{stroke:"#C3C4C7",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"3",clipPath:"url(#a)",children:[(0,ve.jsx)("path",{d:"M15.5 18.75a3.75 3.75 0 1 0 0-7.5 3.75 3.75 0 0 0 0 7.5Z"}),(0,ve.jsx)("path",{d:"M24.5 18.75a2.062 2.062 0 0 0 .413 2.275l.075.075a2.5 2.5 0 1 1-3.538 3.537l-.075-.075a2.062 2.062 0 0 0-2.275-.412 2.063 2.063 0 0 0-1.25 1.888v.212a2.5 2.5 0 0 1-5 0v-.113a2.062 2.062 0 0 0-1.35-1.887 2.062 2.062 0 0 0-2.275.413l-.075.075A2.5 2.5 0 1 1 5.612 21.2l.075-.075A2.063 2.063 0 0 0 6.1 18.85a2.063 2.063 0 0 0-1.887-1.25H4a2.5 2.5 0 0 1 0-5h.112A2.062 2.062 0 0 0 6 11.25a2.062 2.062 0 0 0-.412-2.275L5.513 8.9A2.5 2.5 0 0 1 7.28 4.63a2.5 2.5 0 0 1 1.769.732l.075.075a2.063 2.063 0 0 0 2.275.413h.1a2.063 2.063 0 0 0 1.25-1.887V3.75a2.5 2.5 0 0 1 5 0v.112A2.062 2.062 0 0 0 19 5.75a2.062 2.062 0 0 0 2.275-.412l.075-.075A2.5 2.5 0 1 1 24.887 8.8l-.075.075a2.062 2.062 0 0 0-.412 2.275v.1a2.063 2.063 0 0 0 1.888 1.25h.212a2.5 2.5 0 0 1 0 5h-.113a2.063 2.063 0 0 0-1.887 1.25Z"})]}),(0,ve.jsx)("defs",{children:(0,ve.jsx)("clipPath",{id:"a",children:(0,ve.jsx)("path",{fill:"#fff",d:"M0 0h30v30H0z",transform:"translate(.5)"})})})]}),Be=[{label:(0,_._x)("October 29, 2024",'example date in "F j, Y" format',"the-events-calendar"),value:"F j, Y"},{label:(0,_._x)("29 October, 2024",'example date in "j F, Y" format',"the-events-calendar"),value:"j F, Y"},{label:(0,_._x)("10/29/2024",'example date in "m/d/Y" format',"the-events-calendar"),value:"m/d/Y"},{label:(0,_._x)("29/10/2024",'example date in "d/m/Y" format',"the-events-calendar"),value:"d/m/Y"},{label:(0,_._x)("2024–10–29",'example date in "Y-m-d" format',"the-events-calendar"),value:"Y-m-d"}],He=[{label:(0,_.__)("Sunday","the-events-calendar"),value:"0"},{label:(0,_.__)("Monday","the-events-calendar"),value:"1"},{label:(0,_.__)("Tuesday","the-events-calendar"),value:"2"},{label:(0,_.__)("Wednesday","the-events-calendar"),value:"3"},{label:(0,_.__)("Thursday","the-events-calendar"),value:"4"},{label:(0,_.__)("Friday","the-events-calendar"),value:"5"},{label:(0,_.__)("Saturday","the-events-calendar"),value:"6"}],De=({moveToNextTab:e,skipToNextTab:t})=>{const n=(0,v.useSelect)((e=>e(J).getVisitedFields())),s=(0,v.useDispatch)(J).setVisitedField,{currency:a,timezone_string:i,date_format:r,start_of_week:o,timezones:d,currencies:l}=(0,v.useSelect)((e=>{const t=e(J);return{currency:t.getSetting("currency"),timezone_string:t.getSetting("timezone_string"),date_format:t.getSetting("date_format"),start_of_week:t.getSetting("start_of_week"),timezones:t.getSetting("timezones"),currencies:t.getSetting("currencies")}}),[]),[c,p]=(0,u.useState)(a),[x,g]=(0,u.useState)(i),[b,m]=(0,u.useState)(r||Be[0].value),[j,C]=(0,u.useState)(o||0),[k,L]=(0,u.useState)(!1);let f=(0,_.__)("Please ensure your time zone is correct.","the-events-calendar");i?i.includes("UTC")&&(f=(0,_.__)("Please select your time zone as UTC offsets are not supported.","the-events-calendar")):f=(0,_.__)("Please select your time zone.","the-events-calendar");const w={currency:c,timezone_string:x,date_format:b,start_of_week:j,currentTab:2};(0,u.useEffect)((()=>{const e=e=>{s(e.target.id)},t=document.getElementById("settingsPanel")?.querySelectorAll("input, select, textarea");return t?.forEach((t=>{t.addEventListener("change",e)})),()=>{t?.forEach((t=>{t.removeEventListener("change",e)}))}}),[]),(0,u.useEffect)((()=>{const e={currencyCode:c,timeZone:y(),dateFormat:b,weekStart:j,"visit-at-least-one":S()};L(Object.values(e).every((e=>!!e)))}),[c,x,b,j,n]);const S=()=>{const e=[!!(c&&x&&b&&j)];return["currencyCode","timeZone","dateFormat","weekStart"].some((e=>n.includes(e)))||e},y=()=>{const e="time-zone",t=n.includes(e),s=!t||!!x,a=document.getElementById(e),i=a?.closest(".tec-events-onboarding__form-field");return t&&T(x,a,i,s),s},T=(e,t,n,s)=>{e?s?(n.classList.remove("invalid","empty"),t.classList.remove("invalid")):(n.classList.add("invalid"),t.classList.add("invalid")):(n.classList.add("invalid","empty"),t.classList.add("invalid"))};return(0,ve.jsxs)(ve.Fragment,{children:[(0,ve.jsx)(ze,{}),(0,ve.jsxs)("div",{className:"tec-events-onboarding__tab-header",children:[(0,ve.jsx)("h1",{className:"tec-events-onboarding__tab-heading",children:(0,_.__)("Event Settings","the-events-calendar")}),(0,ve.jsx)("p",{className:"tec-events-onboarding__tab-subheader",children:(0,_.__)("Let's get your events with the correct basic settings.","the-events-calendar")})]}),(0,ve.jsxs)("div",{className:"tec-events-onboarding__tab-content",children:[(0,ve.jsxs)("div",{className:"tec-events-onboarding__form-wrapper",children:[(0,ve.jsxs)(h.BaseControl,{__nextHasNoMarginBottom:!0,id:"currency-code",label:(0,_.__)("Currency symbol","the-events-calendar"),className:"tec-events-onboarding__form-field",children:[(0,ve.jsx)("select",{onChange:e=>p(e.target.value),defaultValue:c,children:Object.entries(l).map((([e,t])=>(0,ve.jsxs)("option",{value:e,children:[t.symbol," (",t.name,")"]},e)))}),(0,ve.jsx)("span",{className:"tec-events-onboarding__required-label",children:(0,_.__)("Currency symbol is required.","the-events-calendar")}),(0,ve.jsx)("span",{className:"tec-events-onboarding__invalid-label",children:(0,_.__)("Currency symbol is invalid.","the-events-calendar")})]}),(0,ve.jsxs)(h.BaseControl,{__nextHasNoMarginBottom:!0,id:"time-zone",label:(0,_.__)("Time zone","the-events-calendar"),className:"tec-events-onboarding__form-field",children:[(0,ve.jsxs)("select",{id:"time-zone",onChange:e=>g(e.target.value),describedby:"time-zone-description",defaultValue:x,children:[(0,ve.jsx)("option",{value:"",children:(0,_.__)("Select a non-UTC timezone.","the-events-calendar")}),Object.entries(d).map((([e,t])=>(0,ve.jsx)("optgroup",{className:"continent",label:e,children:Object.entries(t).map((([e,t])=>(0,ve.jsx)("option",{value:e,children:t},e)))},e)))]}),(0,ve.jsx)("span",{id:"time-zone-description",className:"tec-events-onboarding__field-description",children:f}),(0,ve.jsx)("span",{className:"tec-events-onboarding__required-label",children:(0,_.__)("A non-UTC time zone is required.","the-events-calendar")}),(0,ve.jsx)("span",{className:"tec-events-onboarding__invalid-label",children:(0,_.__)("Time zone is invalid.","the-events-calendar")})]}),(0,ve.jsxs)(h.BaseControl,{__nextHasNoMarginBottom:!0,id:"date-format",label:(0,_.__)("Date format","the-events-calendar"),className:"tec-events-onboarding__form-field",children:[(0,ve.jsx)("select",{id:"date-format",onChange:e=>m(e.target.value),defaultValue:b,children:Be.map((({label:e,value:t})=>(0,ve.jsx)("option",{value:t,children:e},t)))}),(0,ve.jsx)("span",{className:"tec-events-onboarding__required-label",children:(0,_.__)("Date format is required.","the-events-calendar")}),(0,ve.jsx)("span",{className:"tec-events-onboarding__invalid-label",children:(0,_.__)("Date format is invalid.","the-events-calendar")})]}),(0,ve.jsxs)(h.BaseControl,{__nextHasNoMarginBottom:!0,id:"week-starts",label:(0,_.__)("Your week starts on","the-events-calendar"),className:"tec-events-onboarding__form-field",children:[(0,ve.jsx)("select",{id:"week-starts",onChange:e=>C(e.target.value),defaultValue:j,children:He.map((({label:e,value:t})=>(0,ve.jsx)("option",{value:t,children:e},t)))}),(0,ve.jsx)("span",{className:"tec-events-onboarding__required-label",children:(0,_.__)("Currency symbol is required.","the-events-calendar")}),(0,ve.jsx)("span",{className:"tec-events-onboarding__invalid-label",children:(0,_.__)("Currency symbol is invalid.","the-events-calendar")})]})]}),(0,ve.jsx)(fe,{disabled:!k,moveToNextTab:e,tabSettings:w}),(0,ve.jsx)(we,{skipToNextTab:t,currentTab:2})]})]})},Oe=()=>(0,ve.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 37 36",className:"tec-events-onboarding__content-header-icon",role:"presentation",children:[(0,ve.jsx)("path",{fill:"#C3C4C7",fillRule:"evenodd",d:"M18.342 7.275a8.487 8.487 0 0 0-8.487 8.487c0 3.28 2.147 6.53 4.6 9.12a32.464 32.464 0 0 0 3.887 3.471 32.464 32.464 0 0 0 3.887-3.472c2.453-2.59 4.6-5.84 4.6-9.12a8.487 8.487 0 0 0-8.487-8.486Zm0 23.026-.877 1.314v-.001l-.003-.002-.009-.005-.027-.019a21.117 21.117 0 0 1-.441-.31 35.59 35.59 0 0 1-4.822-4.224c-2.58-2.723-5.466-6.743-5.466-11.292a11.645 11.645 0 1 1 23.29 0c0 4.549-2.887 8.569-5.466 11.291a35.59 35.59 0 0 1-4.822 4.225 21.117 21.117 0 0 1-.441.31l-.027.018-.009.006-.002.002h-.001s-.001.001-.877-1.313Zm0 0 .876 1.314a1.58 1.58 0 0 1-1.752 0l.876-1.314Zm.877-1.313Zm-1.754 0Z",clipRule:"evenodd"}),(0,ve.jsx)("path",{fill:"#C3C4C7",fillRule:"evenodd",d:"M18.342 13.986a1.776 1.776 0 1 0 0 3.553 1.776 1.776 0 0 0 0-3.553Zm-4.934 1.776a4.934 4.934 0 1 1 9.869 0 4.934 4.934 0 0 1-9.869 0Z",clipRule:"evenodd"})]}),Re=({moveToNextTab:e,skipToNextTab:t})=>{const n=(0,v.useSelect)((e=>e(J).getSetting("venue")||{id:0,name:"",address:"",city:"",state:"",zip:"",country:"",phone:"",website:""}),[]),s=(0,v.useSelect)((e=>e(J).getSetting("countries")),[]),a=(0,v.useSelect)((e=>e(J).getVisitedFields())),i=(0,v.useDispatch)(J).setVisitedField,r=!!n.venueId,[o,d]=(0,u.useState)(n.venueId||!1),[l,c]=(0,u.useState)(n.name||""),[p,x]=(0,u.useState)(n.address||""),[g,b]=(0,u.useState)(n.city||""),[m,j]=(0,u.useState)(n.state||""),[C,k]=(0,u.useState)(n.zip||""),[L,f]=(0,u.useState)(n.country||"US"),[w,S]=(0,u.useState)(n.phone||""),[y,T]=(0,u.useState)(n.website||""),[M,N]=(0,u.useState)(!!n.venueId||!!n.website||!1),[A,E]=(0,u.useState)(!!n.venueId||!!n.phone||!1),[F,V]=(0,u.useState)(!1);(0,u.useEffect)((()=>{if(o)return void V(!0);const e={"venue-name":I(),"venue-address":Z(),"venue-city":z(),"venue-state":B(),"venue-zip":H(),"venue-country":D(),"venue-phone":O(),"venue-website":R(),"visit-at-least-one":W()};V(Object.values(e).every((e=>!!e)))}),[a,l,p,g,m,C,L,w,y,A,M]);const W=()=>["venue-name","venue-address","venue-city","venue-state","venue-zip","venue-country","venue-phone","venue-website"].some((e=>a.includes(e)));(0,u.useEffect)((()=>{const e=e=>{i(e.target.id)},t=document.getElementById("venuePanel")?.querySelectorAll("input, select, textarea");return t?.forEach((t=>{t.addEventListener("blur",e)})),()=>{t?.forEach((t=>{t.removeEventListener("blur",e)}))}}),[]);const I=()=>{const e="venue-name",t=a.includes(e),n=!!l,s=document.getElementById(e),i=s?.closest(".tec-events-onboarding__form-field");return t&&P(l,s,i,n),n},Z=()=>{if(!p)return!0;const e="venue-address",t=a.includes(e);if(!t)return!0;const n=!!p,s=document.getElementById(e),i=s?.closest(".tec-events-onboarding__form-field");return t&&P(p,s,i,n),n},z=()=>{if(!g)return!0;const e="venue-city",t=a.includes(e);if(!t)return!0;const n=!!g,s=document.getElementById(e),i=s?.closest(".tec-events-onboarding__form-field");return t&&P(g,s,i,n),n},B=()=>{if(!m)return!0;const e="venue-state",t=a.includes(e);if(!t)return!0;const n=!!m,s=document.getElementById(e),i=s?.closest(".tec-events-onboarding__form-field");return t&&P(m,s,i,n),n},H=()=>{if(!C)return!0;const e="venue-zip",t=a.includes(e);if(!t)return!0;const n=!!C&&/^[a-z0-9][a-z0-9\- ]{0,10}[a-z0-9]$/i.test(C),s=document.getElementById(e),i=s?.closest(".tec-events-onboarding__form-field");return t&&P(C,s,i,n),n},D=()=>{const e="venue-country",t=a.includes(e);if(!t)return!0;const n=!!L,s=document.getElementById(e),i=s?.closest(".tec-events-onboarding__form-field");return t&&P(L,s,i,n),n},O=()=>{if(!w)return!0;const e="venue-phone",t=a.includes(e);if(!t)return!0;const n=!A||!!w&&/^\+?\d{1,3}[\s.-]?\(?\d{1,4}\)?[\s.-]?\d{1,4}[\s.-]?\d{1,4}[\s.-]?\d{1,4}$/.test(w),s=document.getElementById(e),i=s?.closest(".tec-events-onboarding__form-field");return t&&P(w,s,i,n),n},R=()=>{if(!y)return!0;const e=a.includes("venue-website");if(!e)return!0;const t=document.getElementById("venue-website"),n=t?.closest(".tec-events-onboarding__form-field");let s=!1;try{const e=new URL(y);s="http:"===e.protocol||"https:"===e.protocol}catch(e){s=!1}return e&&P(y,t,n,s),s},P=(e,t,n,s)=>{e?s?(n.classList.remove("invalid","empty"),t.classList.remove("invalid")):(n.classList.add("invalid"),t.classList.add("invalid")):(n.classList.add("invalid","empty"),t.classList.add("invalid"))},q={venue:{venueId:o,name:l,address:p,city:g,state:m,zip:C,country:L,phone:w,website:y},currentTab:4},U=o>0?(0,_.__)("Looks like you have already created your first venue. Well done!","the-events-calendar"):(0,_.__)("Show your attendees where they need to go to get to your events. You can display the location using Google Maps on your event pages.","the-events-calendar");return(0,ve.jsxs)(ve.Fragment,{children:[(0,ve.jsx)(Oe,{}),(0,ve.jsxs)("div",{className:"tec-events-onboarding__tab-header",children:[(0,ve.jsx)("h1",{className:"tec-events-onboarding__tab-heading",children:(0,_.__)("Add your first event venue","the-events-calendar")}),(0,ve.jsx)("p",{className:"tec-events-onboarding__tab-subheader",children:U})]}),(0,ve.jsxs)("div",{className:"tec-events-onboarding__tab-content",children:[(0,ve.jsxs)("div",{className:"tec-events-onboarding__form-wrapper",children:[(0,ve.jsxs)(h.BaseControl,{__nextHasNoMarginBottom:!0,label:(0,_.__)("Venue name","the-events-calendar"),id:"venue-name",className:"tec-events-onboarding__form-field",children:[(0,ve.jsx)("input",{id:"venue-name",type:"text",onChange:e=>c(e.target.value),defaultValue:l,disabled:r,placeholder:(0,_.__)("Enter venue name","the-events-calendar")}),(0,ve.jsx)("span",{className:"tec-events-onboarding__required-label",children:(0,_.__)("Venue name is required.","the-events-calendar")})]}),(0,ve.jsxs)(h.BaseControl,{__nextHasNoMarginBottom:!0,label:(0,_.__)("Address","the-events-calendar"),id:"venue-address",className:"tec-events-onboarding__form-field",children:[(0,ve.jsx)("input",{id:"venue-address",type:"text",onChange:e=>x(e.target.value),defaultValue:p,disabled:r,placeholder:(0,_.__)("Enter venue street address","the-events-calendar")}),(0,ve.jsx)("span",{className:"tec-events-onboarding__required-label",children:(0,_.__)("Venue address is required.","the-events-calendar")}),(0,ve.jsx)("span",{className:"tec-events-onboarding__invalid-label",children:(0,_.__)("Venue address is invalid.","the-events-calendar")})]}),(0,ve.jsxs)(h.BaseControl,{__nextHasNoMarginBottom:!0,label:(0,_.__)("City","the-events-calendar"),id:"venue-city",className:"tec-events-onboarding__form-field",children:[(0,ve.jsx)("input",{id:"venue-city",type:"text",onChange:e=>b(e.target.value),defaultValue:g,disabled:r,placeholder:(0,_.__)("Enter city","the-events-calendar")}),(0,ve.jsx)("span",{className:"tec-events-onboarding__required-label",children:(0,_.__)("Venue city is required.","the-events-calendar")}),(0,ve.jsx)("span",{className:"tec-events-onboarding__invalid-label",children:(0,_.__)("Venue city is invalid.","the-events-calendar")})]}),(0,ve.jsxs)(h.BaseControl,{__nextHasNoMarginBottom:!0,label:(0,_.__)("State or province","the-events-calendar"),id:"venue-state",className:"tec-events-onboarding__form-field",children:[(0,ve.jsx)("input",{id:"venue-state",onChange:e=>j(e.target.value),defaultValue:m,disabled:r,type:"text",placeholder:(0,_.__)("Enter state or province","the-events-calendar")}),(0,ve.jsx)("span",{className:"tec-events-onboarding__required-label",children:(0,_.__)("Venue state is required.","the-events-calendar")}),(0,ve.jsx)("span",{className:"tec-events-onboarding__invalid-label",children:(0,_.__)("Venue state is invalid.","the-events-calendar")})]}),(0,ve.jsxs)(h.BaseControl,{__nextHasNoMarginBottom:!0,label:(0,_.__)("Zip / postal code","the-events-calendar"),id:"venue-zip",className:"tec-events-onboarding__form-field",children:[(0,ve.jsx)("input",{id:"venue-zip",onChange:e=>k(e.target.value),defaultValue:C,disabled:r,type:"text",placeholder:(0,_.__)("Enter zip or postal code","the-events-calendar")}),(0,ve.jsx)("span",{className:"tec-events-onboarding__required-label",children:(0,_.__)("Venue zip/postal code is required.","the-events-calendar")}),(0,ve.jsx)("span",{className:"tec-events-onboarding__invalid-label",children:(0,_.__)("Venue zip/postal code is invalid.","the-events-calendar")})]}),(0,ve.jsx)(h.BaseControl,{__nextHasNoMarginBottom:!0,id:"venue-country",className:"tec-events-onboarding__form-field",label:(0,_.__)("Country","the-events-calendar"),children:(0,ve.jsx)("select",{onChange:e=>f(e.target.value),defaultValue:L,disabled:r,id:"venue-country",children:Object.entries(s).map((([e,t])=>(0,ve.jsx)("optgroup",{className:"continent",label:e,children:Object.entries(t).map((([e,t])=>(0,ve.jsx)("option",{value:e,children:t},e)))},e)))})}),(0,ve.jsx)("span",{className:"tec-events-onboarding__required-label",children:(0,_.__)("Venue country is required.","the-events-calendar")}),(0,ve.jsx)("span",{className:"tec-events-onboarding__invalid-label",children:(0,_.__)("Venue country is invalid.","the-events-calendar")}),!o&&A?"":(0,ve.jsx)(h.Button,{variant:"tertiary",className:"tec-events-onboarding__form-field-trigger",onClick:e=>E(!0),children:(0,_._x)("Add a phone +","Direction to add an phone followed by a plus sign to indicate it shows a visually hidden field.","the-events-calendar")}),(0,ve.jsxs)(h.BaseControl,{__nextHasNoMarginBottom:!0,className:"tec-events-onboarding__form-field",id:"venue-phone",label:(0,_.__)("Phone","the-events-calendar"),children:[(0,ve.jsx)("input",{id:"venue-phone",onChange:e=>S(e.target.value),defaultValue:w,disabled:r,type:"phone",placeholder:(0,_.__)("Enter phone number","the-events-calendar")}),(0,ve.jsx)("span",{className:"tec-events-onboarding__required-label",children:(0,_.__)("Venue phone is required.","the-events-calendar")}),(0,ve.jsx)("span",{className:"tec-events-onboarding__invalid-label",children:(0,_.__)("Venue phone is invalid.","the-events-calendar")})]}),!o&&M?"":(0,ve.jsx)(h.Button,{variant:"tertiary",className:"tec-events-onboarding__form-field-trigger",onClick:()=>N(!0),children:(0,_._x)("Add a website +","Direction to add a website followed by a plus sign to indicate it shows a visually hidden field.","the-events-calendar")}),(0,ve.jsxs)(h.BaseControl,{__nextHasNoMarginBottom:!0,className:"tec-events-onboarding__form-field",id:"venue-website",label:(0,_.__)("Website","the-events-calendar"),children:[(0,ve.jsx)("input",{id:"venue-website",onChange:e=>T(e.target.value),defaultValue:y,disabled:r,type:"url",placeholder:(0,_.__)("Enter website","the-events-calendar")}),(0,ve.jsx)("span",{className:"tec-events-onboarding__required-label",children:(0,_.__)("Venue website is required.","the-events-calendar")}),y&&!y.toLowerCase().startsWith("http")?(0,ve.jsx)("span",{className:"tec-events-onboarding__invalid-label",children:(0,_.__)('Venue website must start with a protocol, i.e. "https://"',"the-events-calendar")}):(0,ve.jsx)("span",{className:"tec-events-onboarding__invalid-label",children:(0,_.__)("Venue website is invalid.","the-events-calendar")})]})]}),(0,ve.jsx)(fe,{moveToNextTab:e,tabSettings:q,disabled:!F}),(0,ve.jsx)(we,{skipToNextTab:t,currentTab:4})]})]})},Pe=()=>(0,ve.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 47 35",className:"tec-events-onboarding__content-header-icon tec-events-onboarding__content-header-icon--brand",alt:"Event Tickets Logo",children:[(0,ve.jsx)("path",{fill:"#fff",d:"m8.36.278.053.043 4.81 4.363a.572.572 0 0 1 .034.817 3.315 3.315 0 0 0-.898 2.272c0 1.85 1.515 3.35 3.382 3.35.965 0 1.862-.402 2.5-1.094.219-.238.637.273.877.491l3.925 3.858s5.612-4.799 6.541-6.172c.189-.279.328-.675.595-.48l.051.041 4.442 4.061c.25.229.25.617.001.846l-.119.113a4.226 4.226 0 1 0 6.32 5.582.596.596 0 0 1 .817-.116l.053.043 4.431 4.052c.21.192.249.503.094.739-3.564 5.41-12.863 10.833-17.943 11.325-4.456.431-7.52-.161-10.959-2.376l-.22-.144c-2.34-1.547-6.633-5.031-12.897-10.466l-.8-.695C1.566 19.77.5 17.818.587 14.729.716 10.223 3.206 5.105 7.577.36a.596.596 0 0 1 .782-.08Z"}),(0,ve.jsx)("path",{fill:"#334AFF",fillRule:"evenodd",d:"M34.166 32.417c3.383-1.373 9.74-5.891 12.25-9.59l-5.067-4.433a4.854 4.854 0 0 1-3.8 1.806c-2.655 0-4.808-2.085-4.808-4.656 0-1.286 1.408-3.292 1.408-3.292l-4.3-4.06c-1.153 1.652-3.533 4.366-6.572 6.553-1.399 1.007-2.938 1.902-4.561 2.531",clipRule:"evenodd"}),(0,ve.jsx)("path",{fill:"#0F1031",fillRule:"evenodd",d:"M8.413.321 8.359.278a.596.596 0 0 0-.782.08C3.206 5.105.717 10.223.588 14.73c-.088 3.09.979 5.043 2.861 6.002l.8.695c6.265 5.435 10.557 8.92 12.898 10.466l.22.144c3.439 2.215 6.503 2.807 10.959 2.376 5.08-.492 14.38-5.915 17.943-11.325a.572.572 0 0 0-.093-.739l-4.432-4.052-.053-.043a.596.596 0 0 0-.816.116 4.226 4.226 0 1 1-6.32-5.582l.118-.113a.572.572 0 0 0-.002-.846l-4.44-4.06-.052-.043a.595.595 0 0 0-.84.145c-1.407 2.08-3.496 4.043-6.58 6.42l-.271.2c-5.994 4.37-11.475 6.498-16.855 5.71-2.528-.37-3.967-1.975-3.868-5.438l.009-.23c.19-3.994 2.383-8.57 6.225-12.899l.055-.062 3.967 3.598-.053.074a4.452 4.452 0 0 0-.786 2.53c0 2.488 2.04 4.507 4.56 4.507l.161-.003a4.573 4.573 0 0 0 2.753-1.038l.03-.026 2.06 1.868a.596.596 0 0 0 .831-.033.572.572 0 0 0-.033-.818l-2.468-2.238a.596.596 0 0 0-.836.037 3.384 3.384 0 0 1-2.499 1.093c-1.867 0-3.382-1.499-3.382-3.35 0-.854.324-1.657.898-2.271a.572.572 0 0 0-.034-.817L8.413.32Zm21.454 8.827.058-.08 3.51 3.21-.076.09a5.355 5.355 0 0 0-1.223 3.41c0 2.972 2.417 5.384 5.402 5.384l.175-.003a5.392 5.392 0 0 0 3.647-1.577l.029-.03 3.616 3.306-.106.148c-3.62 4.93-12.13 9.812-16.689 10.254-4.294.416-7.12-.157-10.406-2.328l-.336-.227c-2.027-1.389-5.384-4.09-10.06-8.096L6.02 21.417c5.483.613 10.973-1.539 16.883-5.791l.573-.418.314-.243c2.708-2.109 4.658-3.918 6.076-5.817Z",clipRule:"evenodd"}),(0,ve.jsx)("path",{fill:"#fff",fillRule:"evenodd",d:"M34.855 25.84a1.494 1.494 0 0 0 .09 2.14 1.567 1.567 0 0 0 2.182-.089 1.492 1.492 0 0 0-.09-2.14 1.567 1.567 0 0 0-2.182.089ZM30.221 21.801a1.493 1.493 0 0 0 .09 2.139 1.566 1.566 0 0 0 2.182-.088 1.493 1.493 0 0 0-.09-2.14 1.567 1.567 0 0 0-2.182.089ZM25.587 17.766a1.494 1.494 0 0 0 .09 2.14 1.567 1.567 0 0 0 2.183-.09 1.493 1.493 0 0 0-.09-2.139 1.567 1.567 0 0 0-2.183.089Z",clipRule:"evenodd"})]}),qe=({moveToNextTab:e,skipToNextTab:t})=>{const n=(0,v.useSelect)((e=>e(J).getSetting("event-tickets-installed")||!1),[]),s=(0,v.useSelect)((e=>e(J).getSetting("event-tickets-active")||!1),[]),[a,i]=(0,u.useState)(!0),r={eventTickets:a,currentTab:5},o=n?(0,_.__)("Activate the Event Tickets Plugin for me.","the-events-calendar"):(0,_.__)("Yes, install and activate Event Tickets for free on my website.","the-events-calendar");return(0,ve.jsxs)(ve.Fragment,{children:[(0,ve.jsx)(Pe,{}),(0,ve.jsxs)("div",{className:"tec-events-onboarding__tab-header",children:[(0,ve.jsx)("h1",{className:"tec-events-onboarding__tab-heading",children:(0,_.__)("Event Tickets","the-events-calendar")}),(0,ve.jsx)("p",{className:"tec-events-onboarding__tab-subheader",children:(0,_.__)("Will you be selling tickets or providing attendees the ability to RSVP to your events?","the-events-calendar")})]}),(0,ve.jsxs)("div",{className:"tec-events-onboarding__tab-content",children:[!s&&(0,ve.jsxs)("div",{alignment:"top",justify:"center",spacing:0,className:"tec-events-onboarding__checkbox tec-events-onboarding__checkbox--tickets",children:[(0,ve.jsx)(h.CheckboxControl,{__nextHasNoMarginBottom:!0,"aria-describedby":"tec-events-onboarding__checkbox-description",checked:a,onChange:i,id:"tec-events-onboarding__tickets-checkbox-input"}),(0,ve.jsxs)("div",{className:"tec-events-onboarding__checkbox-description",children:[(0,ve.jsx)("label",{htmlFor:"tec-events-onboarding__tickets-checkbox-input",children:o}),(0,ve.jsx)("div",{id:"tec-events-onboarding__checkbox-description"})]})]}),(0,ve.jsx)(fe,{tabSettings:r,moveToNextTab:e,disabled:!1}),(0,ve.jsx)(we,{skipToNextTab:t,currentTab:5,buttonText:(0,_.__)("Skip and finish setup","the-events-calendar")})]})]})},Ue=()=>(0,ve.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 37 36",className:"tec-events-onboarding__content-header-icon",alt:"presentation",children:(0,ve.jsx)("path",{fill:"#C3C4C7",fillRule:"evenodd",d:"M14.761 11.415a3.622 3.622 0 1 1 7.244 0 3.622 3.622 0 0 1-7.244 0ZM18.383 4.5a6.915 6.915 0 1 0 0 13.83 6.915 6.915 0 0 0 0-13.83Zm-5.268 15.805A6.915 6.915 0 0 0 6.2 27.22v2.634a1.646 1.646 0 0 0 3.293 0V27.22a3.622 3.622 0 0 1 3.622-3.622H23.65a3.622 3.622 0 0 1 3.622 3.622v2.634a1.646 1.646 0 0 0 3.293 0V27.22a6.915 6.915 0 0 0-6.915-6.915H13.115Z",clipRule:"evenodd"})}),Ge=({moveToNextTab:e,skipToNextTab:t})=>{const n=(0,v.useSelect)((e=>e(J).getSetting("organizer")||{id:0,name:"",phone:"",website:"",email:""}),[]),s=(0,v.useSelect)((e=>e(J).getVisitedFields())),a=(0,v.useDispatch)(J).setVisitedField,[i,r]=(0,u.useState)(n.organizerId||!1),[o,d]=(0,u.useState)(n.name||""),[l,c]=(0,u.useState)(n.phone||""),[p,x]=(0,u.useState)(n.website||""),[g,b]=(0,u.useState)(n.email||""),[m,j]=(0,u.useState)(!!n.organizerId||!!n.phone||!1),[C,k]=(0,u.useState)(!!n.organizerId||!!n.website||!1),[L,f]=(0,u.useState)(!!n.organizerId||!!n.email||!1),[w,S]=(0,u.useState)(!1),y=!!n.organizerId;(0,u.useEffect)((()=>{const e=e=>{a(e.target.id)},t=document.getElementById("organizerPanel")?.querySelectorAll("input, select, textarea");return t?.forEach((t=>{t.addEventListener("blur",e)})),()=>{t?.forEach((t=>{t.removeEventListener("blur",e)}))}}),[]);const T=(e,t,n,s)=>{e?s?(n.classList.remove("invalid","empty"),t.classList.remove("invalid")):(n.classList.add("invalid"),t.classList.add("invalid")):(n.classList.add("invalid","empty"),t.classList.add("invalid"))};(0,u.useEffect)((()=>{if(i)return void S(!0);const e={"organizer-name":N(),"organizer-phone":E(),"organizer-website":F(),"organizer-email":A(),"visit-at-least-one":M()};S(Object.values(e).every((e=>!!e)))}),[o,l,p,g,m,C,L,s]);const M=()=>["organizer-name","organizer-phone","organizer-website","organizer-email"].some((e=>s.includes(e))),N=()=>{const e="organizer-name",t=s.includes(e),n=!!o,a=document.getElementById(e),i=a?.closest(".tec-events-onboarding__form-field");return t&&T(o,a,i,n),n},A=()=>{if(!g)return!0;const e="organizer-email",t=s.includes(e);if(!t)return!0;const n=/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(g),a=document.getElementById(e),i=a?.closest(".tec-events-onboarding__form-field");return t&&T(g,a,i,n),n},E=()=>{if(!l)return!0;const e="organizer-phone",t=s.includes(e);if(!t)return!0;const n=/^\+?\d{1,3}[\s.-]?\(?\d{1,4}\)?[\s.-]?\d{1,4}[\s.-]?\d{1,4}[\s.-]?\d{1,4}$/.test(l),a=document.getElementById(e),i=a?.closest(".tec-events-onboarding__form-field");return t&&T(l,a,i,n),n},F=()=>{if(!p)return!0;const e="organizer-website",t=s.includes(e);if(!t)return!0;const n=document.getElementById(e),a=n?.closest(".tec-events-onboarding__form-field");let i=!1;try{const e=new URL(p);i="http:"===e.protocol||"https:"===e.protocol}catch(e){i=!1}return t&&T(p,n,a,i),i},V=(e,t)=>{t(!0)},W={organizer:{organizerId:i,name:o,phone:l,website:p,email:g},currentTab:3},I=i>0?(0,_.__)("Looks like you have already created your first organizer. Well done!","the-events-calendar"):(0,_.__)("Add an event organizer for your events. You can display this information for your event attendees on your website.","the-events-calendar");return(0,ve.jsxs)(ve.Fragment,{children:[(0,ve.jsx)(Ue,{}),(0,ve.jsxs)("div",{className:"tec-events-onboarding__tab-header",children:[(0,ve.jsx)("h1",{className:"tec-events-onboarding__tab-heading",children:(0,_.__)("Add your first event organizer","the-events-calendar")}),(0,ve.jsx)("p",{className:"tec-events-onboarding__tab-subheader",children:I})]}),(0,ve.jsxs)("div",{className:"tec-events-onboarding__tab-content",children:[(0,ve.jsxs)("div",{className:"tec-events-onboarding__form-wrapper",children:[(0,ve.jsxs)(h.BaseControl,{__nextHasNoMarginBottom:!0,id:"organizer-name",className:"tec-events-onboarding__form-field",label:(0,_.__)("Organizer name","the-events-calendar"),children:[(0,ve.jsx)("input",{type:"text",id:"organizer-name",onChange:e=>d(e.target.value),defaultValue:o,disabled:y,placeholder:(0,_.__)("Enter organizer name","the-events-calendar")}),(0,ve.jsx)("span",{className:"tec-events-onboarding__required-label",children:(0,_.__)("Organizer name is required.","the-events-calendar")})]}),!i&&m?"":(0,ve.jsx)(h.Button,{__next40pxDefaultSize:!0,onClick:e=>V(0,j),variant:"tertiary",className:"tec-events-onboarding__form-field-trigger",children:(0,_._x)("Add a phone number +","Direction to add a phone number followed by a plus sign to indicate it shows a visually hidden field.","the-events-calendar")}),(0,ve.jsxs)(h.BaseControl,{__nextHasNoMarginBottom:!0,className:"tec-events-onboarding__form-field",id:"organizer-phone",label:(0,_.__)("Phone","the-events-calendar"),children:[(0,ve.jsx)("input",{id:"organizer-phone",onChange:e=>c(e.target.value),type:"tel",defaultValue:l,disabled:!m||y,placeholder:(0,_.__)("Enter phone number","the-events-calendar")}),(0,ve.jsx)("span",{className:"tec-events-onboarding__required-label",children:(0,_.__)("Organizer phone is required.","the-events-calendar")}),(0,ve.jsx)("span",{className:"tec-events-onboarding__invalid-label",children:(0,_.__)("Organizer phone is invalid.","the-events-calendar")})]}),!i&&C?"":(0,ve.jsx)(h.Button,{__next40pxDefaultSize:!0,onClick:e=>V(0,k),variant:"tertiary",className:"tec-events-onboarding__form-field-trigger",children:(0,_._x)("Add a website +","Direction to add a website followed by a plus sign to indicate it shows a visually hidden field.","the-events-calendar")}),(0,ve.jsxs)(h.BaseControl,{__nextHasNoMarginBottom:!0,className:"tec-events-onboarding__form-field",id:"organizer-website",label:(0,_.__)("Website","the-events-calendar"),children:[(0,ve.jsx)("input",{id:"organizer-website",onChange:e=>x(e.target.value),type:"url",defaultValue:p,disabled:!C||y,placeholder:(0,_.__)("Enter website","the-events-calendar")}),(0,ve.jsx)("span",{className:"tec-events-onboarding__required-label",children:(0,_.__)("Organizer website is required.","the-events-calendar")}),p&&!p.toLowerCase().startsWith("http")?(0,ve.jsx)("span",{className:"tec-events-onboarding__invalid-label",children:(0,_.__)('Organizer website must start with a protocol, i.e. "https://"',"the-events-calendar")}):(0,ve.jsx)("span",{className:"tec-events-onboarding__invalid-label",children:(0,_.__)("Organizer website is invalid.","the-events-calendar")})]}),!i&&L?"":(0,ve.jsx)(h.Button,{__next40pxDefaultSize:!0,onClick:e=>V(0,f),variant:"tertiary",className:"tec-events-onboarding__form-field-trigger",children:(0,_._x)("Add an email +","Direction to add an email followed by a plus sign to indicate it shows a visually hidden field.","the-events-calendar")}),(0,ve.jsxs)(h.BaseControl,{__nextHasNoMarginBottom:!0,className:"tec-events-onboarding__form-field",id:"organizer-email",label:(0,_.__)("Email","the-events-calendar"),children:[(0,ve.jsx)("input",{id:"organizer-email",onChange:e=>b(e.target.value),type:"email",defaultValue:g,disabled:!L||y,placeholder:(0,_.__)("Enter email","the-events-calendar")}),(0,ve.jsx)("span",{className:"tec-events-onboarding__required-label",children:(0,_.__)("Organizer email is required.","the-events-calendar")}),(0,ve.jsx)("span",{className:"tec-events-onboarding__invalid-label",children:(0,_.__)("Organizer email is invalid.","the-events-calendar")})]})]}),(0,ve.jsx)(fe,{disabled:!w,moveToNextTab:e,tabSettings:W}),(0,ve.jsx)(we,{skipToNextTab:t,currentTab:3})]})]})},Ye=()=>{const e=[{id:"welcome",title:(0,_.__)("Welcome","the-events-calendar"),content:Le,ref:(0,i.useRef)(null)},{id:"display",title:(0,_.__)("Display","the-events-calendar"),content:Ze,ref:(0,i.useRef)(null)},{id:"settings",title:(0,_.__)("Settings","the-events-calendar"),content:De,ref:(0,i.useRef)(null)},{id:"organizer",title:(0,_.__)("Organizer","the-events-calendar"),content:Ge,ref:(0,i.useRef)(null)},{id:"venue",title:(0,_.__)("Venue","the-events-calendar"),content:Re,ref:(0,i.useRef)(null)},{id:"tickets",title:(0,_.__)("Tickets","the-events-calendar"),content:qe,ref:(0,i.useRef)(null)}],{closeModal:t}=(0,v.useDispatch)(he),n=(0,v.useSelect)((e=>e(J).getSetting("currentTab")))||0,s=(0,v.useSelect)((e=>e(J).getSkippedTabs()))||[],a=(0,v.useSelect)((e=>e(J).getCompletedTabs()))||[],[r,o]=(0,u.useState)((()=>e.map(((e,t)=>({...e,disabled:t>n}))))),[d,l]=(0,u.useState)(0),c=(e,t)=>{o((n=>n.map(((n,s)=>s===e?{...n,...t}:n))))};(0,u.useEffect)((()=>{var e;n>0&&((e=n)>0&&e<r.length&&(((e,t)=>{o((n=>n.map(((n,s)=>s<e?{...n,...t}:n))))})(e,{completed:a.includes(e),skipped:s.includes(e),disabled:!1}),c(e,{disabled:!1}),l(e)))}),[n]);const h=()=>{d<r.length-1?(c(d,{completed:!0}),c(d+1,{disabled:!1}),l((e=>{const t=e+1;return r[t].ref.current.focus(),t}))):t()},p=()=>{d<r.length-1?(c(d+1,{disabled:!1}),l((e=>{const t=e+1;return r[t].ref.current.focus(),t}))):t()},x=e=>{r[e].disabled||l(e)},g=e=>{const t=d+e;t>=0&&t<r.length&&!r[t].disabled&&(l(t),r[t].ref.current.focus())};return(0,ve.jsxs)("section",{className:`tec-events-onboarding__tabs tec-events-onboarding__tab-${r[d].id}`,children:[(0,ve.jsxs)("div",{className:"tec-events-onboarding__tabs-header",children:[(0,ve.jsx)(_e,{}),(0,ve.jsx)("ul",{role:"tablist",className:"tec-events-onboarding__tabs-list","aria-label":"Onboarding Tabs",onKeyDown:e=>{"ArrowRight"===e.key&&g(1),"ArrowLeft"===e.key&&g(-1)},children:r.map(((e,t)=>(0,ve.jsx)(xe,{index:t,tab:e,activeTab:d,handleChange:x},e.id)))})]}),r.map(((e,t)=>(0,ve.jsx)(pe,{tabIndex:t,id:`${e.id}Panel`,tabId:e.id,activeTab:d,children:(0,ve.jsx)(e.content,{moveToNextTab:h,skipToNextTab:p})},e.id)))]})},$e=({bootData:e})=>{const[t,n]=(0,i.useState)(!1),{initializeSettings:s}=(0,v.useDispatch)(J),{openModal:a}=(0,v.useDispatch)(he),{closeModal:r}=(0,v.useDispatch)(he);(0,u.useEffect)((()=>{s(e),n(!0)}),[]);const o=(0,v.useSelect)((e=>e(J).getSetting("finished"))),d=(0,v.useSelect)((e=>e(J).getSetting("begun"))),l=(0,v.useSelect)((e=>e(he).getIsOpen()));return(0,u.useEffect)((()=>{t&&!o&&a()}),[d,o,t]),(0,ve.jsx)(ve.Fragment,{children:l&&(0,ve.jsx)(h.Modal,{overlayClassName:"tec-events-onboarding__modal-overlay",className:"tec-events-onboarding__modal",contentLabel:"TEC Onboarding Wizard",isDismissible:!1,isFullScreen:!0,initialTabName:"intro",onRequestClose:r,selectOnMove:!1,shouldCloseOnEsc:!1,shouldCloseOnClickOutside:!1,children:(0,ve.jsx)(Ye,{})})})};let Ke=!1;d()((()=>{const e=document.getElementById("tec-events-onboarding-wizard");if(!e||Ke)return;const t=e.dataset.containerElement,n=e.dataset.wizardBootData;if(!t||!n)return void console.warn("Container element or boot data is missing.");const s=document.getElementById(t);if(!s)return void console.warn(`Container with ID '${t}' not found.`);let a;try{a=JSON.parse(n)}catch(e){return void console.error("Failed to parse bootData:",e)}c().createRoot(s).render((0,ve.jsx)($e,{bootData:a})),Ke=!0}))})(),window.tec=window.tec||{},window.tec.events=window.tec.events||{},window.tec.events.wizard={}})();