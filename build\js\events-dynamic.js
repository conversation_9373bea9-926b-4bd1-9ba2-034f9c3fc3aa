(()=>{var t=t||{};!function(t,e){"use strict";e.field_class=".event-dynamic-helper-text",e.date_fmt_settings={dateSettings:{days:tribe_dynamic_help_text.days,daysShort:tribe_dynamic_help_text.daysShort,months:tribe_dynamic_help_text.months,monthsShort:tribe_dynamic_help_text.monthsShort}},e.date_fmt=new DateFormatter(e.date_fmt_settings),e.text=JSON.parse(tribe_dynamic_help_text.msgs),e.date_with_year=tribe_dynamic_help_text.date_with_year,e.date_no_year=tribe_dynamic_help_text.date_no_year,e.datepicker_format=tribe_dynamic_help_text.datepicker_format,e.dynamic_text,e.start_date,e.start_time,e.end_date,e.end_time,e.all_day="",e.init=function(){e.setup_and_display_text(),e.event_date_change()},e.setup_and_display_text=function(){e.update(),e.msg_logic(),e.parse_and_display_text()},e.update=function(){e.start_date=t("#EventStartDate").val(),e.start_time=t("#EventStartTime").val(),e.end_date=t("#EventEndDate").val(),e.end_time=t("#EventEndTime").val(),e.all_day=!!t("#allDayCheckbox").prop("checked")||"",e.start_date===e.end_date&&new Date(e.start_time).toTimeString().substring(0,5)>new Date(e.end_time).toTimeString().substring(0,5)&&(void 0!==t.fn.tribeTimepicker?t("#EventEndTime").tribeTimepicker("setTime",e.start_time):t("#EventEndTime").timepicker("setTime",e.start_time),e.end_time=e.start_time)},e.msg_logic=function(){e.start_date!=e.end_date||e.all_day||e.start_time==e.end_time?e.start_date!=e.end_date||e.all_day||e.start_time!=e.end_time?e.start_date==e.end_date&&e.all_day?e.dynamic_text=e.text[2]:e.start_date==e.end_date||e.all_day||e.start_time==e.end_time?e.start_date==e.end_date||e.all_day||e.start_time!=e.end_time?e.start_date!=e.end_date&&e.all_day&&(e.dynamic_text=e.text[5]):e.dynamic_text=e.text[4]:e.dynamic_text=e.text[3]:e.dynamic_text=e.text[1]:e.dynamic_text=e.text[0]},e.parse_and_display_text=function(){e.dynamic_text=e.dynamic_text.replace("%%starttime%%",e.start_time),e.dynamic_text=e.dynamic_text.replace("%%endtime%%",e.end_time),e.dynamic_text=e.dynamic_text.replace("%%startdatewithyear%%",e.date_formatter(e.start_date,e.datepicker_format,e.date_with_year)),e.dynamic_text=e.dynamic_text.replace("%%enddatewithyear%%",e.date_formatter(e.end_date,e.datepicker_format,e.date_with_year)),e.dynamic_text=e.dynamic_text.replace("%%startdatenoyear%%",e.date_formatter(e.start_date,e.datepicker_format,e.date_no_year)),e.dynamic_text=e.dynamic_text.replace("%%enddatenoyear%%",e.date_formatter(e.end_date,e.datepicker_format,e.date_no_year)),t(e.field_class).html(e.dynamic_text)},e.date_formatter=function(t,a,_){return e.date_fmt.formatDate(e.date_fmt.parseDate(t,a),_)},e.event_date_change=function(){t("#EventStartDate, #EventStartTime, #EventEndDate, #EventEndTime, #allDayCheckbox").on("change",(function(){e.setup_and_display_text()}))},t((function(){t("#eventDetails, #event_datepickers").hasClass("eventForm")&&e.init()}))}(jQuery,t),window.tec=window.tec||{},window.tec.events=window.tec.events||{},window.tec.events.eventsDynamic={}})();