window.tribe.onboarding={},function(n,i){"use strict";const t=n(document);i.selectors={},i.getTooltipClasses=function(n){return["tribe-onboarding__tooltip"].concat(n).join(" ")},i.initTour=function(){const n=TribeOnboardingTour.steps,t=TribeOnboardingTour.classes||[];void 0!==n&&n.length&&introJs().setOptions({tooltipClass:i.getTooltipClasses(t),steps:n}).start()},i.initHints=function(){const n=TribeOnboardingHints.hints,t=TribeOnboardingHints.classes||[];Array.isArray(n)&&n.length&&introJs().setOptions({tooltipClass:i.getTooltipClasses(t),hintButtonLabel:TribeOnboarding.hintButtonLabel,hintPosition:"middle-right",hintAnimation:!0,hints:n}).addHints()},i.ready=function(){i.initTour(),i.initHints()},t.ready(i.ready)}(jQuery,window.tribe.onboarding),window.tec=window.tec||{},window.tec.common=window.tec.common||{},window.tec.common.onboarding={};