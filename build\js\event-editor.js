(()=>{var e=e||{};!function(e,t){"use strict";t.selectors={featuredEventCheckbox:'input[name="feature_event"]',stickyInMonthViewCheckbox:'input[name="EventShowInCalendar"]'},t.auto_enable_sticky_field=function(){e(this).prop("checked")&&e(t.selectors.stickyInMonthViewCheckbox).prop("checked",!0)},t.bindFeaturedEvents=function(){e(t.selectors.featuredEventCheckbox).on("change",t.auto_enable_sticky_field),e(t).trigger("event-editor-post-init.tribe")},t.init=function(){t.bindFeaturedEvents(),wp.widgets&&wp.blocks&&!wp.blocks.getBlockType("core/legacy-widget")&&wp.widgets.registerLegacyWidgetBlock()},e(t.init)}(j<PERSON><PERSON><PERSON>,e),window.tec=window.tec||{},window.tec.events=window.tec.events||{},window.tec.events.eventEditor={}})();