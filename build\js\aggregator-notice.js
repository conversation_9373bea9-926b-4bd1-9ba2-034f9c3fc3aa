(()=>{var e,r;window.tribe_aggregator=window.tribe_aggregator||{},window.tribe_aggregator.notice=window.tribe_aggregator.notice||{},e=jQuery,(r=window.tribe_aggregator).notice={selector:{notice:".tribe-notice-aggregator-update-msg",progress:".progress",tracker:".tracker",created:".track-created .value",updated:".track-updated .value",skipped:".track-skipped .value",remaining:".track-remaining .value",bar:".bar"},progress:{}},r.notice.progress.init=function(){r.notice.progress.data={},r.notice.progress.$={},r.notice.progress.$.notice=e(".tribe-notice-aggregator-update-msg"),r.notice.progress.$.spinner=r.notice.progress.$.notice.find("img"),r.notice.progress.$.progress=r.notice.progress.$.notice.find(r.notice.selector.progress),r.notice.progress.$.tracker=r.notice.progress.$.notice.find(r.notice.selector.tracker),r.notice.progress.$.created=r.notice.progress.$.tracker.find(r.notice.selector.created),r.notice.progress.$.updated=r.notice.progress.$.tracker.find(r.notice.selector.updated),r.notice.progress.$.skipped=r.notice.progress.$.tracker.find(r.notice.selector.skipped),r.notice.progress.$.remaining=r.notice.progress.$.tracker.find(r.notice.selector.remaining),r.notice.progress.$.bar=r.notice.progress.$.notice.find(r.notice.selector.bar),r.notice.progress.data.time=Date.now(),r.notice.progress.hasHeartBeat="undefined"!=typeof wp&&wp.heartbeat,r.notice.progress.hasHeartBeat&&wp.heartbeat.interval(15),setTimeout(r.notice.progress.start)},r.notice.progress.start=function(){"object"==typeof window.tribe_aggregator_save&&(r.notice.progress.update(window.tribe_aggregator_save.progress,window.tribe_aggregator_save.progressText),r.notice.progress.hasHeartBeat||r.notice.progress.send_request())},r.notice.progress.continue=!0,e(document).on("heartbeat-send",(function(e,t){"object"==typeof window.tribe_aggregator_save&&r.notice.progress.continue&&(t.ea_record=window.tribe_aggregator_save.record_id)})),e(document).on("heartbeat-tick",(function(e,t){t.ea_progress&&r.notice.progress.handle_response(t.ea_progress)})),r.notice.progress.handle_response=function(e){e.html&&r.notice.progress.data.notice.html(e.html),isNaN(parseInt(e.progress,10))||r.notice.progress.update(e),r.notice.progress.continue=e.continue,e.continue&&!r.notice.progress.hasHeartBeat&&setTimeout(r.notice.progress.send_request,15e3),e.error?(r.notice.progress.$.notice.find(".tribe-message").html(e.error_text),r.notice.progress.$.tracker.remove(),r.notice.progress.$.notice.find(".progress-container").remove(),r.notice.progress.$.notice.removeClass("notice-warning").addClass("notice-error")):e.complete&&(r.notice.progress.$.notice.find(".tribe-message").html(e.complete_text),r.notice.progress.$.tracker.remove(),r.notice.progress.$.notice.find(".progress-container").remove(),r.notice.progress.$.notice.removeClass("notice-warning").addClass("notice-success"),r.notice.progress.$.notice.show())},r.notice.progress.send_request=function(){var t={record:window.tribe_aggregator_save.record_id,check:window.tribe_aggregator_save.check,action:"tribe_aggregator_realtime_update"};e.post(ajaxurl,t,r.notice.progress.handle_response,"json")},r.notice.progress.update=function(e){var t=parseInt(e.progress,10);if(!(t<0||t>100)&&void 0!==e.counts){var o=["created","updated","skipped"];for(var s in o)if(e.counts[o[s]]){var n=e.counts[o[s]],i=r.notice.progress.$[o[s]];"updated"===o[s]||"skipped"===o[s]?n>(i?i.html():0)&&i.html(n):i.html(n),r.notice.progress.$.tracker.hasClass("has-"+o[s])||r.notice.progress.$.tracker.addClass("has-"+o[s])}r.notice.progress.$.bar.css("width",t+"%"),r.notice.progress.$.progress.attr("title",e.progress_text)}},r.notice.progress.remove_notice=function(){r.notice.progress.$.notice.animate({opacity:0,height:"toggle"},1e3,(function(){r.notice.progress.$.notice.remove()}))},e(document).on("tribe_aggregator_init_notice",(function(){r.notice.progress.init()})),r.notice.progress.init(),window.tec=window.tec||{},window.tec.events=window.tec.events||{},window.tec.events.aggregatorNotice={}})();