tribe.events=tribe.events||{},tribe.events.views=tribe.events.views||{},tribe.events.views.datepicker={},function(e,t){"use strict";const a=e(document);function n(){e(".datepicker:visible").each((function(){var t=e(this);t.find("th.prev").attr("role","button").attr("tabindex","0").attr("aria-label","Previous month").off("keydown.a11y").on("keydown.a11y",(function(t){"Enter"!==t.key&&" "!==t.key&&13!==t.keyCode&&32!==t.keyCode||(e(this).trigger("click"),t.preventDefault())})),t.find("th.next").attr("role","button").attr("tabindex","0").attr("aria-label","Next month").off("keydown.a11y").on("keydown.a11y",(function(t){"Enter"!==t.key&&" "!==t.key&&13!==t.keyCode&&32!==t.keyCode||(e(this).trigger("click"),t.preventDefault())})),t.find("th.datepicker-switch").attr("role","button").attr("tabindex","0").attr("aria-label","Select month").off("keydown.a11y").on("keydown.a11y",(function(t){"Enter"!==t.key&&" "!==t.key&&13!==t.keyCode&&32!==t.keyCode||(e(this).trigger("click"),t.preventDefault())}))}))}t.selectors={datepickerFormClass:".tribe-events-c-top-bar__datepicker-form",datepickerContainer:'[data-js="tribe-events-top-bar-datepicker-container"]',datepickerDaysBody:".datepicker-days tbody",input:'[data-js="tribe-events-top-bar-date"]',button:'[data-js="tribe-events-top-bar-datepicker-button"]',buttonOpenClass:".tribe-events-c-top-bar__datepicker-button--open",dateInput:'[name="tribe-events-views[tribe-bar-date]"]',prevIconTemplate:".tribe-events-c-top-bar__datepicker-template-prev-icon",nextIconTemplate:".tribe-events-c-top-bar__datepicker-template-next-icon"},t.state={initialized:!1},t.options={container:null,daysOfWeekDisabled:[],maxViewMode:"decade",minViewMode:"month",orientation:"bottom left",showOnFocus:!1,templates:{leftArrow:"",rightArrow:""}},t.keyCode={ENTER:13},t.today=null,t.dateFormatMap={d:"dd",j:"d",m:"mm",n:"m",Y:"yyyy"},t.observer=null,t.padNumber=function(e){const t=e+"";return(t.length>1?"":"0")+t},t.request=function(e,t){const a={view_data:e};tribe.events.views.manager.request(a,t)},t.createDateInputObj=function(t){const a=e("<input>");return a.attr({type:"hidden",name:"tribe-events-views[tribe-bar-date]",value:t}),a},t.submitRequest=function(e,a){const n={};n["tribe-bar-date"]=a,t.request(n,e)},t.handleChangeDate=function(e){const a=e.data.container,n=e.date.getDate(),o=e.date.getMonth()+1,i=e.date.getFullYear(),r=t.padNumber(n),s=[i,t.padNumber(o),r].join("-");t.submitRequest(a,s)},t.handleChangeMonth=function(e){const a=e.data.container;let n,o;if(e.date)n=e.date.getMonth()+1,o=e.date.getFullYear();else{const e=a.find(t.selectors.input).bootstrapDatepicker("getDate");n=e.getMonth()+1,o=e.getFullYear()}const i=[o,t.padNumber(n)].join("-");t.submitRequest(a,i)},t.handleKeyDown=function(e){e.keyCode===t.keyCode.ENTER&&e.data.input.bootstrapDatepicker().trigger("changeMonth")},t.handleShow=function(e){e.data.datepickerButton.addClass(t.selectors.buttonOpenClass.className())},t.handleHide=function(e){const a=e.data.datepickerButton,n=a.data("tribeEventsState");e.data.observer.disconnect(),n.isTarget?e.data.input.bootstrapDatepicker("show"):a.removeClass(t.selectors.buttonOpenClass.className()).trigger("focus")},t.handleMousedown=function(e){const a=e.data.target,n=a.data("tribeEventsState");if("touchstart"===e.type){const e="hide"==(a.hasClass(t.selectors.buttonOpenClass.className())?"hide":"show");return n.isTarget=!1,void a.data("tribeTapHide",e).data("tribeEventsState",n).off("mousedown",t.handleMousedown)}n.isTarget=!0,a.data("tribeEventsState",n)},t.handleClick=function(e){const a=e.data.input,n=e.data.target,o=n.data("tribeEventsState"),i=n.hasClass(t.selectors.buttonOpenClass.className())?"hide":"show";n.data("tribeTapHide")||(o.isTarget=!1,n.data("tribeEventsState",o),a.bootstrapDatepicker(i),"show"===i&&a.trigger("focus"))},t.handleMutation=function(e){const a=e.container;return function(e,n){e.forEach((function(e){"childList"===e.type&&a.find(t.selectors.datepickerDaysBody).is(e.target)&&e.addedNodes.length&&a.trigger("handleMutationMonthChange.tribeEvents")}))}},t.setToday=function(e){let a=e;e.indexOf(" ")>=0&&(a=e.split(" ")[0]),t.today=new Date(a)},t.isSameAsToday=function(e,a){switch(a){case"year":return e.getFullYear()===t.today.getUTCFullYear();case"month":return t.isSameAsToday(e,"year")&&e.getMonth()===t.today.getUTCMonth();case"day":return t.isSameAsToday(e,"month")&&e.getDate()===t.today.getUTCDate();default:return!1}},t.isBeforeToday=function(e,a){switch(a){case"year":return e.getFullYear()<t.today.getUTCFullYear();case"month":return t.isBeforeToday(e,"year")||t.isSameAsToday(e,"year")&&e.getMonth()<t.today.getUTCMonth();case"day":return t.isBeforeToday(e,"month")||t.isSameAsToday(e,"month")&&e.getDate()<t.today.getUTCDate();default:return!1}},t.filterDayCells=function(e){return t.isBeforeToday(e,"day")?"past":t.isSameAsToday(e,"day")?"current":void 0},t.filterMonthCells=function(e){return t.isBeforeToday(e,"month")?"past":t.isSameAsToday(e,"month")?"current":void 0},t.filterYearCells=function(e){return t.isBeforeToday(e,"year")?"past":t.isSameAsToday(e,"year")?"current":void 0},t.convertDateFormat=function(e){let a=e;return Object.keys(t.dateFormatMap).forEach((function(e){a=a.replace(e,t.dateFormatMap[e])})),a},t.initDateFormat=function(e){const a=(e.date_formats||{}).compact,n=t.convertDateFormat(a);t.options.format=n},t.deinit=function(e,a,n){const o=e.data.container;o.trigger("beforeDatepickerDeinit.tribeEvents",[a,n]);const i=o.find(t.selectors.input),r=o.find(t.selectors.button);i.bootstrapDatepicker("destroy").off(),r.off(),o.off("beforeAjaxSuccess.tribeEvents",t.deinit),o.trigger("afterDatepickerDeinit.tribeEvents",[a,n])},t.init=function(e,a,n,o){n.trigger("beforeDatepickerInit.tribeEvents",[a,n,o]);const i=n.find(t.selectors.input),r=n.find(t.selectors.button),s=n.find(t.selectors.prevIconTemplate).html(),d=n.find(t.selectors.nextIconTemplate).html(),c=o.slug,l="month"===c,u=l?"changeMonth":"changeDate",p=l?t.handleChangeMonth:t.handleChangeDate;t.observer=new MutationObserver(t.handleMutation({container:n})),t.setToday(o.today),t.initDateFormat(o),t.options.weekStart=o.start_of_week,t.options.container=n.find(t.selectors.datepickerContainer),t.options.minViewMode=l?"year":"month";const b=(window.tribe_l10n_datatables||{}).datepicker||{},h=b.nextText||"Next",f=b.prevText||"Prev";t.options.templates.leftArrow=s+'<span class="tribe-common-a11y-visual-hide">'+f+"</span>",t.options.templates.rightArrow=d+'<span class="tribe-common-a11y-visual-hide">'+h+"</span>",t.options.beforeShowDay=t.filterDayCells,t.options.beforeShowMonth=t.filterMonthCells,t.options.beforeShowYear=t.filterYearCells,document.dir&&"rtl"===document.dir&&(t.options.rtl=!0),document.lang&&(t.options.language=document.lang),i.bootstrapDatepicker(t.options).on(u,{container:n},p).on("show",{datepickerButton:r},t.handleShow).on("hide",{datepickerButton:r,input:i,observer:t.observer},t.handleHide),l&&i.bootstrapDatepicker().on("keydown",{input:i},t.handleKeyDown),r.on("touchstart mousedown",{target:r},t.handleMousedown).on("click",{target:r,input:i},t.handleClick).data("tribeEventsState",{isTarget:!1}),n.on("beforeAjaxSuccess.tribeEvents",{container:n,viewSlug:c},t.deinit),n.trigger("afterDatepickerInit.tribeEvents",[a,n,o])},t.initDatepickerI18n=function(){const t=(window.tribe_l10n_datatables||{}).datepicker||{};t.dayNames&&(e.fn.bootstrapDatepicker.dates.en.days=t.dayNames),t.dayNamesShort&&(e.fn.bootstrapDatepicker.dates.en.daysShort=t.dayNamesShort),t.dayNamesMin&&(e.fn.bootstrapDatepicker.dates.en.daysMin=t.dayNamesMin),t.monthNames&&(e.fn.bootstrapDatepicker.dates.en.months=t.monthNames),t.monthNamesMin&&(e.fn.bootstrapDatepicker.dates.en.monthsShort=t.monthNamesMin),t.today&&(e.fn.bootstrapDatepicker.dates.en.today=t.today),t.clear&&(e.fn.bootstrapDatepicker.dates.en.clear=t.clear)},t.initDatepicker=function(){t.initDatepickerI18n(),t.state.initialized=!0},t.ready=function(){t.initDatepicker(),t.state.initialized&&a.on("afterSetup.tribeEvents",tribe.events.views.manager.selectors.container,t.init)},e(t.ready),new MutationObserver((function(t){for(const a of t)"childList"!==a.type&&"subtree"!==a.type||e(a.addedNodes).each((function(){(e(this).hasClass&&e(this).hasClass("datepicker")||e(this).find&&e(this).find(".datepicker").length)&&n()})),"attributes"===a.type&&e(a.target).hasClass("datepicker")&&n()})).observe(document.body,{childList:!0,subtree:!0,attributes:!0}),e(document).ready((function(){n()}))}(jQuery,tribe.events.views.datepicker),window.tec=window.tec||{},window.tec.events=window.tec.events||{},window.tec.events.views=window.tec.events.views||{},window.tec.events.views.datepicker={};