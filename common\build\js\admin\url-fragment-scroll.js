tribe.urlFragmentScroll=tribe.urlFragmentScroll||{},function(n,t){"use strict";const o=n(document);t.setup=function(){t.navigateToFragment(window.location.href),o.on("click",".tribe_events_page_tribe-common",t.onClick)},t.onClick=function(o){const i=n(o.target).attr("href");void 0!==i&&t.navigateToFragment(i)},t.navigateToFragment=function(n){const o=t.getUrlFragment(n);o&&t.adjustScrollPosition(o)},t.adjustScrollPosition=function(t){if(!n("#wpadminbar").length)return;const o=n("#"+t).position();o&&setTimeout((function(){window.scroll(window.scrollX,o.top)}))},t.getUrlFragment=function(n){const t=n.match(/#([a-z0-9_-]+)$/i);return null===t?"":t[1]},n(t.setup)}(j<PERSON>uery,tribe.urlFragmentScroll),window.tec=window.tec||{},window.tec.common=window.tec.common||{},window.tec.common.admin=window.tec.common.admin||{},window.tec.common.admin.urlFragmentScroll={};