window.tribe_dropdowns=window.tribe_dropdowns||{},function(e,t,a){"use strict";function r(e,t){if(!a.isArray(t))return!1;for(const o in t){const n=t[o];if(n.hasOwnProperty("id")&&n.id==e.id)return n;if(n.hasOwnProperty("text")&&n.text==e.text)return n;if(n.hasOwnProperty("children")&&a.isArray(n.children)){const t=r(e,n.children);if(t)return t}}return!1}t.selector={dropdown:".tribe-dropdown",created:".tribe-dropdown-created",searchField:".select2-search__field"},e.fn.tribe_dropdowns=function(){return t.dropdown(this,{}),this},t.freefrom_create_search_choice=function(e){if("string"!=typeof e.term)return null;const t=e.term.trim();if(""===t)return null;const r=this.options.options,o=r.$select;if(t.match(r.regexToken)&&(!o.is("[data-int]")||o.is("[data-int]")&&t.match(/\d+/))){const e={id:t,text:t,new:!0};return o.is("[data-create-choice-template]")&&(e.text=a.template(o.data("createChoiceTemplate"))({term:t})),e}return null},t.search_id=function(e){let t;return void 0!==e.id?t=e.id:void 0!==e.ID?t=e.ID:void 0!==e.value&&(t=e.value),void 0===e?void 0:t},t.matcher=function(r,o){if("string"!=typeof r.term||""===r.term.trim())return o;if(void 0===o.text)return null;const n=r.term.trim(),s=o.text,i=e(o.element).closest("select").data("dropdown");let d=-1!==s.toUpperCase().indexOf(n.toUpperCase());if(!d&&void 0!==i.tags){const e=a.where(i.tags,{text:s});i.tags.length>0&&a.isObject(e)&&(d=-1!==t.search_id(e[0]).toUpperCase().indexOf(n.toUpperCase()))}return d},t.init_selection=function(t,a){const o=t.is("[multiple]"),n=t.data("dropdown"),s=t.val().split(n.regexSplit),i=[];e(s).each((function(e,a){const o=r({id:this,text:this},n.ajax?t.data("options"):n.data);o&&o.selected&&i.push(o)})),i.length&&o?a(i):i.length?a(i[0]):a(!1)},t.getSelectClasses=function(e){const t=e.attr("class").split(/\s+/);return a.difference(t,["select2-hidden-accessible","hide-before-select2-init"])},t.element=function(r,o){const n=e(r);let s;if(o=e.extend({},o),n.addClass(t.selector.created.className()),o.$select=n,o.dropdownAutoWidth=!0,o.width="resolve",o.containerCss={},n.is(":visible")&&(o.containerCss.display="inline-block",o.containerCss.position="relative"),o.dropdownCss={},o.dropdownCss.width="auto",n.is("[data-dropdown-css-width]")&&(o.dropdownCss.width=n.data("dropdown-css-width"),o.dropdownCss.width&&"false"!==o.dropdownCss.width||(delete o.dropdownCss.width,delete o.containerCss)),o.allowClear=!0,n.is("[data-prevent-clear]")&&(o.allowClear=!1),n.is("[data-searching-placeholder]")&&(o.formatSearching=n.data("searching-placeholder")),!n.is("[data-placeholder]")&&n.is("[placeholder]")&&(o.placeholder=n.attr("placeholder")),n.is("[data-options]")&&(o.data=n.data("options")),o.minimumResultsForSearch=10,n.is("[data-hide-search]")&&(o.minimumResultsForSearch=1/0),n.is("[data-force-search]")&&delete o.minimumResultsForSearch,n.is("[data-freeform]")&&(o.createTag=t.freefrom_create_search_choice,o.tags=!0),n.is("[multiple]")&&(o.multiple=!0,n.is("[data-maximum-selection-size]")&&(o.maximumSelectionSize=n.data("maximum-selection-size")),n.is("data-separator")||n.data("separator",","),a.isArray(n.data("separator"))?o.tokenSeparators=n.data("separator"):o.tokenSeparators=[n.data("separator")],o.separator=n.data("separator"),o.regexSeparatorElements=["^("],o.regexSplitElements=["(?:"],e.each(o.tokenSeparators,(function(e,t){o.regexSeparatorElements.push("[^"+t+"]+"),o.regexSplitElements.push("["+t+"]")})),o.regexSeparatorElements.push(")$"),o.regexSplitElements.push(")"),o.regexSeparatorString=o.regexSeparatorElements.join(""),o.regexSplitString=o.regexSplitElements.join(""),o.regexToken=new RegExp(o.regexSeparatorString,"ig"),o.regexSplit=new RegExp(o.regexSplitString,"ig")),n.is("[data-tags]")){const e=n.data("tags");o.tags=1===e||"1"===e||"true"===e,o.tags&&(o.createSearchChoice=function(e,t){if(e.match(o.regexToken))return{id:e,text:e}},0===o.tags.length&&(o.formatNoMatches=function(){return n.attr("placeholder")}))}if(n.is("[data-source]")){const a=n.data("source");if(o.data={results:[]},o.formatResult=function(t,a,r){return void 0!==t.breadcrumbs?e.merge(t.breadcrumbs,[t.text]).join(" &#187; "):t.text},o.ajax={dataType:"json",type:"POST",url:t.ajaxurl(),processResults:(t,a)=>e.isPlainObject(t)&&void 0!==t.success?e.isPlainObject(t.data)&&void 0!==t.data.results?t.success?t.data:("string"===e.type(t.data.message)?console.error(t.data.message):console.error("The Select2 search failed in some way... Verify the source."),{results:[]}):(console.error("We received a malformed results array, could not complete the Select2 Search."),{results:[]}):(console.error("We received a malformed Object, could not complete the Select2 Search."),{results:[]})},n.is("[data-ajax-delay]")&&(o.ajax.delay=n.data("ajax-delay")),n.is("[data-ajax-cache]")){const e=n.data("ajax-cache");o.ajax.cache=1===e||"1"===e||"true"===e}n.is("[data-minimum-input-length]")&&(o.minimumInputLength=parseInt(n.data("minimum-input-length"))),o.ajax.data=function(e,t){return{action:"tribe_dropdown",source:a,search:e,page:t,args:n.data("source-args"),nonce:n.data("source-nonce")}}}n.is("[data-attach-container]")&&(n.is("[multiple]")?(e.fn.select2.amd.define("AttachedDropdownAdapter",["select2/utils","select2/dropdown","select2/dropdown/attachContainer"],(function(e,t,a){return e.Decorate(t,a)})),o.dropdownAdapter=e.fn.select2.amd.require("AttachedDropdownAdapter")):(e.fn.select2.amd.define("AttachedWithSearchDropdownAdapter",["select2/utils","select2/dropdown","select2/dropdown/search","select2/dropdown/minimumResultsForSearch","select2/dropdown/attachContainer"],(function(e,t,a,r,o){let n=e.Decorate(t,o);return n=e.Decorate(n,a),n=e.Decorate(n,r),n})),o.dropdownAdapter=e.fn.select2.amd.require("AttachedWithSearchDropdownAdapter"))),n.data("dropdown",o),s=n.select2TEC(o),n.is("[data-clear-to-value]")&&s.on("select2:unselect",(function(){e(this).data("openingAfterUnselect",1)})).on("select2:opening",(function(t){const a=e(this);if(!a.data("openingAfterUnselect"))return;a.data("openingAfterUnselect",0);const r=a.data("clear-to-value");a.val(r).trigger("change"),t.preventDefault()}));const i=t.getSelectClasses(n).join(" ");s.data("select2").$container.addClass(i),s.data("select2").$container.removeClass("hide-before-select2-init"),s.on("select2:open",t.action_select2_open)},t.ajaxurl=function(){return void 0!==window.ajaxurl?window.ajaxurl:"undefined"!=typeof TEC&&void 0!==TEC.ajaxurl?TEC.ajaxurl:void console.error("Dropdowns framework cannot properly do an AJAX request without the WordPress `ajaxurl` variable setup.")},t.action_select2_open=function(a){const r=e(this),o=r.data("select2"),n=o.$dropdown.find(t.selector.searchField),s=t.getSelectClasses(o.$element).reduce((function(e,t){return"hide-if-js"===t||"tribe-dropdown-created"===t?e:e+" "+t}));o.$dropdown.addClass(s),r.is("[data-search-placeholder]")&&n.attr("placeholder",r.data("searchPlaceholder"))},t.dropdown=function(e,a){const r=e.not(".select2-offscreen, .select2-container, "+t.selector.created.className());return 0===r.length||(a||(a={}),r.each((function(e,r){t.element(r,a)}))),r},e((function(){e(t.selector.dropdown).tribe_dropdowns()})),e(window).on("unload",(function(){e(t.selector.dropdown).tribe_dropdowns()}))}(jQuery,window.tribe_dropdowns,window.underscore||window._),window.tec=window.tec||{},window.tec.common=window.tec.common||{},window.tec.common.dropdowns={};