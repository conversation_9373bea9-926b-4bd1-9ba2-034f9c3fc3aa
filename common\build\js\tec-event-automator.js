window.tribe.events=window.tribe.events||{},tribe.events.automatorSettingsAdmin=tribe.events.automatorSettingsAdmin||{},function(t,e,n){const o=t(document);e.selectors={integrationContainer:".tec-automator-settings",integrationAdd:".tec-automator-settings__add-api-key-button",messageWrap:".tec-automator-settings-message__wrap",integrationList:".tec-automator-settings-items__wrap",integrationItem:".tec-automator-settings-details__container",integrationName:".tec-automator-settings-details__name-input",integrationUser:".tec-settings-form__users-dropdown",integrationGenerate:".tec-automator-settings-details-action__generate",integrationRevoke:".tec-automator-settings-details-action__revoke",copyButton:".tec-automator-settings__copy-btn",copyButtonTxt:".tec-automator-settings__copy-btn-text",copySuccess:".copy-success",copyFail:".copy-fail",dashboardContainer:".tec-automator-dashboard",endpointContainer:".tec-settings-connection-endpoint-dashboard-details__container",endpointActionButton:".tec-settings-connection-endpoint-dashboard-details-action__button",endpointClearButton:".tec-settings-connection-endpoint-dashboard-details-actions__clear",endpointDisableButton:".tec-settings-connection-endpoint-dashboard-details-actions__disable",endpointEnableButton:".tec-settings-connection-endpoint-dashboard-details-actions__enable",automatorLoader:".tribe-common-c-loader",automatorLoaderHiddenElement:".tribe-common-a11y-hidden"},e.scrollToBottom=function(n){let o=0;n.find(e.selectors.integrationItem).each((function(){o+=t(this).outerHeight()})),t(e.selectors.integrationList).animate({scrollTop:o},500)},e.validateFields=function(t){const n=t.find(e.selectors.integrationName).val(),o=t.find(`${e.selectors.integrationUser} option:selected`).val();return!(!n||!o)},e.onAddApiKeyFieldsSuccess=function(o,s){const i=t(s).closest(e.selectors.integrationContainer),a=t(o).filter(e.selectors.messageWrap),c=t(o).filter(e.selectors.integrationItem);i.find(e.selectors.messageWrap).html(a),0!==c.length&&(i.find(e.selectors.integrationList).append(c),t(e.selectors.integrationList).find(n.selector.dropdown).not(n.selector.created).tribe_dropdowns(),e.hide(i),e.scrollToBottom(i))},e.handleAddApiKey=function(n){n.preventDefault();const o=t(n.target).attr("href"),s=t(n.target).closest(e.selectors.integrationContainer);e.show(s),t.ajax(o,{contentType:"application/json",context:t(e.selectors.integrationList),success:t=>e.onAddApiKeyFieldsSuccess(t,n.target)})},e.onGenerateKeySuccess=function(n,s){const i=t(s).closest(e.selectors.integrationContainer),a=t(n).filter(e.selectors.messageWrap),c=t(n).filter(e.selectors.integrationItem);if(i.find(e.selectors.messageWrap).html(a),e.hide(i),0===c.length)return;const r=c.data("consumerId");o.find(`[data-consumer-id='${r}']`).replaceWith(c),e.scrollToBottom(i)},e.handleGenerateKey=function(n){n.preventDefault();const o=t(n.target).closest(e.selectors.integrationGenerate),s=o.data("ajaxGenerateUrl"),i=o.closest(e.selectors.integrationItem);e.validateFields(i)||window.alert(o.data("generateError"));const a=i.data("consumerId"),c=i.find(e.selectors.integrationName).val(),r=i.find(`${e.selectors.integrationUser} option:selected`).val(),l=o.closest(e.selectors.integrationContainer);e.show(l),t.ajax(s,{contentType:"application/json",context:o.closest(e.selectors.integrationItem),data:{consumer_id:a,name:c,user_id:r,permissions:"read"},success:t=>e.onGenerateKeySuccess(t,o)})},e.onRevokeSuccess=function(n){const o=t(e.selectors.integrationContainer);o.find(e.selectors.messageWrap).html(n),e.hide(o),t(`${e.selectors.integrationItem}.to-delete`).remove()},e.handleRevoke=function(n){n.preventDefault();const o=t(n.target).closest(e.selectors.integrationRevoke),s=o.data("ajaxRevokeUrl"),i=o.closest(e.selectors.integrationItem),a=i.data("consumerId");if(!confirm(o.data("confirmation")))return;const c=o.closest(e.selectors.integrationContainer);e.show(c),i.addClass("to-delete"),t.ajax(s,{contentType:"application/json",context:o.closest(e.selectors.integrationItem),data:{consumer_id:a},success:e.onRevokeSuccess})},e.setupClipboard=function(){o.on("click",`${e.selectors.copyButton}, ${e.selectors.copySuccess}, ${e.selectors.copyFail}`,(function(t){t.preventDefault()}));const t=new ClipboardJS(e.selectors.copyButton),n=tec_automator.clipboard_btn_text;t.on("success",(function(t){const o=t.trigger.querySelector(e.selectors.copyButtonTxt);t.clearSelection(),o.innerHTML=`\n\t\t\t\t<span class="${e.selectors.copySuccess.replace(/\./g,"")}">\n\t\t\t\t\t${tec_automator.clipboard_copied_text}\n\t\t\t\t<span>`,window.setTimeout((function(){o.innerHTML=n}),5e3)})),t.on("error",(function(t){const o=t.trigger.querySelector(e.selectors.copyButtonTxt);o.innerHTML=`\n\t\t\t\t<span class="${e.selectors.copyFail.replace(/\./g,"")}">\n\t\t\t\t\t${tec_automator.clipboard_fail_text}\n\t\t\t\t<span>`,window.setTimeout((function(){o.innerHTML=n}),5e3)}))},e.handleEndpointAction=function(n){n.preventDefault();const o=t(n.target).closest(e.selectors.endpointActionButton),s=o.data("ajaxActionUrl"),i=o.closest(e.selectors.endpointContainer),a=i.data("endpointId");if(!confirm(o.data("confirmation")))return;const c=o.closest(e.selectors.dashboardContainer);e.show(c),i.addClass("to-update"),t.ajax(s,{contentType:"application/json",context:o.closest(e.selectors.endpointContainer),data:{endpoint_id:a},success:t=>e.onEndpointActionSuccess(t,o)})},e.onEndpointActionSuccess=function(n,o){const s=t(o).closest(e.selectors.dashboardContainer),i=t(n).filter(e.selectors.messageWrap);s.find(e.selectors.messageWrap).html(i),e.hide(s);const a=t(n).filter(e.selectors.endpointContainer);0!==a.length&&t(`${e.selectors.endpointContainer}.to-update`).replaceWith(a)},e.show=function(t){const n=t.find(e.selectors.automatorLoader);n.length&&n.removeClass(e.selectors.automatorLoaderHiddenElement.className())},e.hide=function(t){const n=t.find(e.selectors.automatorLoader);n.length&&n.addClass(e.selectors.automatorLoaderHiddenElement.className())},e.bindEvents=function(){o.on("click",e.selectors.integrationGenerate,e.handleGenerateKey).on("click",e.selectors.integrationRevoke,e.handleRevoke).on("click",e.selectors.endpointClearButton,e.handleEndpointAction).on("click",e.selectors.endpointDisableButton,e.handleEndpointAction).on("click",e.selectors.endpointEnableButton,e.handleEndpointAction),t(e.selectors.integrationContainer).on("click",e.selectors.integrationAdd,e.handleAddApiKey)},e.unbindEvents=function(){},e.ready=function(){e.setupClipboard(),e.bindEvents()},t(e.ready)}(jQuery,window.tribe.events.automatorSettingsAdmin,window.tribe_dropdowns),window.tec=window.tec||{},window.tec.common=window.tec.common||{},window.tec.common.tecEventAutomator={};