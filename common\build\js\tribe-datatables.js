window.tribe_data_table=null,function(e){"use strict";e.fn.tribeDataTable=function(t){const a=e.extend({language:{lengthMenu:tribe_l10n_datatables.length_menu,emptyTable:tribe_l10n_datatables.emptyTable,info:tribe_l10n_datatables.info,infoEmpty:tribe_l10n_datatables.info_empty,infoFiltered:tribe_l10n_datatables.info_filtered,zeroRecords:tribe_l10n_datatables.zero_records,search:tribe_l10n_datatables.search,paginate:{next:tribe_l10n_datatables.pagination.next,previous:tribe_l10n_datatables.pagination.previous},aria:{sortAscending:tribe_l10n_datatables.aria.sort_ascending,sortDescending:tribe_l10n_datatables.aria.sort_descending},select:{rows:{0:tribe_l10n_datatables.select.rows[0],_:tribe_l10n_datatables.select.rows._,1:tribe_l10n_datatables.select.rows[1]}}},lengthMenu:[[10,25,50,-1],[10,25,50,tribe_l10n_datatables.pagination.all]]},t);let c=!1;this.is(".dataTable")&&(c=!0);var o={setVisibleCheckboxes(e,t,a){const c=e.find("thead"),n=e.find("tfoot"),l=c.find(".column-cb input:checkbox"),s=n.find(".column-cb input:checkbox");void 0===a&&(a=!1),e.find("tbody .check-column input:checkbox").prop("checked",a),l.prop("checked",a),s.prop("checked",a),a?(t.rows({page:"current"}).select(),o.addGlobalCheckboxLine(e,t)):(e.find(".tribe-datatables-all-pages-checkbox").remove(),t.rows().deselect())},addGlobalCheckboxLine(t,a){t.find(".tribe-datatables-all-pages-checkbox").remove();const c=t.find("thead"),n=e("<a>").attr("href","#select-all").text(tribe_l10n_datatables.select_all_link),l=e("<div>").css("text-align","center").text(tribe_l10n_datatables.all_selected_text).append(n),s=e("<th>").attr("colspan",a.columns()[0].length).append(l),i=e("<tr>").addClass("tribe-datatables-all-pages-checkbox").append(s);n.one("click",(function(e){return a.rows().select(),n.text(tribe_l10n_datatables.clear_selection).one("click",(function(){return o.setVisibleCheckboxes(t,a,!1),e.preventDefault(),!1})),e.preventDefault(),!1})),c.append(i)},togglePageCheckbox(e,t){const a=e.closest(".dataTable");o.setVisibleCheckboxes(a,t,e.is(":checked"))},toggleRowCheckbox(e,t){const a=e.closest("tr");if(e.is(":checked")){t.row(a).select();const c=e.closest(".dataTable tbody").find(".check-column");for(const e of c){const a=e.querySelector("input"),c=e.closest("tr");a.checked&&t.row(c).select()}}else t.row(a).deselect(),e.closest(".dataTable").find("thead .column-cb input:checkbox, tfoot .column-cb input:checkbox").prop("checked",!1)},toggleMultipleRowCheckboxes(e,t){const a=e.closest(".dataTable tbody").find(".check-column");for(const e of a){const a=e.querySelector("input"),c=e.closest("tr");a.checked&&t.row(c).select()}}};return this.each((function(){const t=e(this);let n;n=c?t.DataTable():t.DataTable(a),window.tribe_data_table=n,void 0!==a.data&&(n.clear().draw(),n.rows.add(a.data),n.draw());const l=function(e,a){o.setVisibleCheckboxes(t,n,!1)};t.on({"order.dt":l,"search.dt":l,"length.dt":l}),t.on("click","thead .column-cb input:checkbox, tfoot .column-cb input:checkbox",(function(){o.togglePageCheckbox(e(this),n)})),t.on("click","tbody .check-column input:checkbox",(function(){o.toggleRowCheckbox(e(this),n)})),t.on("change","tbody .check-column input:checkbox",(function(){o.toggleMultipleRowCheckboxes(e(this),n)}))}))}}(jQuery),window.tec=window.tec||{},window.tec.common=window.tec.common||{},window.tec.common.tribeDatatables={};