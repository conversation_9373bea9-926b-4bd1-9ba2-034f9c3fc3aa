tribe.events=tribe.events||{},tribe.events.views=tribe.events.views||{},tribe.events.views.monthGrid={},function(e,t){"use strict";const n=e(document);t.selectors={grid:'[data-js="tribe-events-month-grid"]',row:'[data-js="tribe-events-month-grid-row"]',cell:'[data-js="tribe-events-month-grid-cell"]',focusable:"[tabindex]",focused:'[tabindex="0"]'},t.keyCode={END:35,HOME:36,LEFT:37,UP:38,RIGHT:39,DOWN:40},t.isValidCell=function(e,t,n){return!isNaN(t)&&!isNaN(n)&&t>=0&&n>=0&&e&&e.length&&t<e.length&&n<e[t].length},t.getNextCell=function(e,n,r,i,o){const s=n+o,c=r+i;return t.isValidCell(e,s,c)?{row:s,col:c}:{row:n,col:r}},t.setFocusPointer=function(e,n,r){const i=e.data("tribeEventsState");return!!t.isValidCell(i.grid,n,r)&&(i.grid[i.currentRow][i.currentCol].attr("tabindex","-1"),i.grid[n][r].attr("tabindex","0"),i.currentRow=n,i.currentCol=r,e.data("tribeEventsState",i),!0)},t.focusCell=function(e,n,r){t.setFocusPointer(e,n,r)&&e.data("tribeEventsState").grid[n][r].focus()},t.handleKeydown=function(e){const n=e.data.grid,r=n.data("tribeEventsState"),i=e.which||e.keyCode;let o,s=r.currentRow,c=r.currentCol;switch(i){case t.keyCode.UP:o=t.getNextCell(r.grid,s,c,0,-1),s=o.row,c=o.col;break;case t.keyCode.DOWN:o=t.getNextCell(r.grid,s,c,0,1),s=o.row,c=o.col;break;case t.keyCode.LEFT:o=t.getNextCell(r.grid,s,c,-1,0),s=o.row,c=o.col;break;case t.keyCode.RIGHT:o=t.getNextCell(r.grid,s,c,1,0),s=o.row,c=o.col;break;case t.keyCode.HOME:e.ctrlKey&&(s=0),c=0;break;case t.keyCode.END:e.ctrlKey&&(s=r.grid.length-1),c=r.grid[r.currentRow].length-1;break;default:return}t.focusCell(n,s,c),e.preventDefault()},t.handleClick=function(n){const r=n.data.grid,i=r.data("tribeEventsState"),o=e(n.target).closest(t.selectors.focusable);for(let e=0;e<i.grid.length;e++)for(let n=0;n<i.grid[e].length;n++)if(i.grid[e][n].is(o))return void t.focusCell(r,e,n)},t.initState=function(e){e.data("tribeEventsState",{grid:[],currentRow:0,currentCol:0})},t.setupGrid=function(n){const r=n.data("tribeEventsState");n.find(t.selectors.row).each((function(n,i){const o=[];e(i).find(t.selectors.cell).each((function(n,i){const s=e(i);if(s.is(t.selectors.focusable))s.is(t.selectors.focused)&&(r.currentRow=r.grid.length,r.currentCol=o.length),o.push(s);else{const e=s.find(t.selectors.focusable);e.is(t.selectors.focusable)&&(s.is(t.selectors.focused)&&(r.currentRow=r.grid.length,r.currentCol=o.length),o.push(e))}})),o.length&&r.grid.push(o)})),n.data("tribeEventsState",r)},t.unbindEvents=function(e){e.off()},t.bindEvents=function(e){e.on("keydown",{grid:e},t.handleKeydown).on("click",{grid:e},t.handleClick)},t.deinit=function(e,n,r){const i=e.data.container,o=i.find(t.selectors.grid);t.unbindEvents(o),i.off("beforeAjaxSuccess.tribeEvents",t.deinit)},t.init=function(e,n,r,i){const o=r.find(t.selectors.grid);if(!o.length)return;t.initState(o),t.setupGrid(o);const s=o.data("tribeEventsState");t.setFocusPointer(o,s.currentRow,s.currentCol),t.bindEvents(o),r.on("beforeAjaxSuccess.tribeEvents",{container:r},t.deinit)},t.ready=function(){n.on("afterSetup.tribeEvents",tribe.events.views.manager.selectors.container,t.init)},e(t.ready)}(jQuery,tribe.events.views.monthGrid),window.tec=window.tec||{},window.tec.events=window.tec.events||{},window.tec.events.views=window.tec.events.views||{},window.tec.events.views.monthGrid={};