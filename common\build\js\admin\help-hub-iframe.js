window.tribe=window.tribe||{},tribe.helpPage=tribe.helpPage||{},window.DocsBotAI=window.DocsBotAI||{},tribe.helpPage.BeaconManager={initStub(){window.Beacon&&window.Beacon.readyQueue||(window.Beacon=(t,e,n)=>{window.Beacon.readyQueue.push({method:t,options:e,data:n})},window.Beacon.readyQueue=[])},isAvailable:()=>"function"==typeof window.Beacon,call(t,...e){this.isAvailable()&&window.Beacon(t,...e)},init(t){this.call("init",t)},config(t){this.call("config",t)},on(t,e){this.call("on",t,e)},identify(t){this.call("identify",t)},open(){this.call("open")},close(){this.call("close")}},((t,e)=>{"use strict";function n(t,e){this.beaconKey=t,this.userIdentifiers=e||null,this.scriptLoaded=!1,this.beaconReady=!1,this.initPromise=null}e.selectors={body:"body",helpHubPageID:"help-hub-page",docsbotWidget:"docsbot-widget-embed",optOutMessage:".tec-help-hub-iframe-opt-out-message"},e.DocsBotAIcss="\n\t\t\t\t\t\t/* DocsBot iframe box dimensions */\n\t\t\t\t\t\t.docsbot-iframe-box {\n\t\t\t\t\t\t\theight: 740px;\n\t\t\t\t\t\t\tmax-height: 740px;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t/* Container styling */\n\t\t\t\t\t\t.docsbot-chat-container {\n\t\t\t\t\t\t\tfont-family: Arial, Helvetica, sans-serif;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t/* Inner container styling */\n\t\t\t\t\t\t.docsbot-chat-inner-container {\n\t\t\t\t\t\t\tborder-radius: 0;\n\t\t\t\t\t\t\tbackground-color: #ffffff !important;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t/* Bot message styling */\n\t\t\t\t\t\t.docsbot-chat-bot-message {\n\t\t\t\t\t\t\tborder-color: #334aff;\n\t\t\t\t\t\t\tbackground: #E6E9FF !important;\n\t\t\t\t\t\t\tcolor: #000000 !important;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t/* Header styling */\n\t\t\t\t\t\t.docsbot-chat-header {\n\t\t\t\t\t\t\tbackground-color: #ffffff !important;\n\t\t\t\t\t\t\tborder-bottom: solid 1px #C3C4C7;\n\t\t\t\t\t\t\tcolor: #000000 !important;\n\t\t\t\t\t\t\tpadding: 10px 24px;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t/* Header content styling */\n\t\t\t\t\t\t.docsbot-chat-header-content h1 {\n\t\t\t\t\t\t\ttext-align: left;\n\t\t\t\t\t\t}\n\t\t\t\t\t\t.docsbot-chat-header-content span {\n\t\t\t\t\t\t\tdisplay: none;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t/* Suggested questions container styling */\n\t\t\t\t\t\t.docsbot-chat-suggested-questions-container button {\n\t\t\t\t\t\t\tbackground-color: #F6F7F7 !important;\n\t\t\t\t\t\t\tborder: solid 1px #C3C4C7 !important;\n\t\t\t\t\t\t\tborder-radius: 3px;\n\t\t\t\t\t\t\tcolor: #000000 !important;\n\t\t\t\t\t\t}\n\t\t\t\t\t\t.docsbot-chat-suggested-questions-container span {\n\t\t\t\t\t\t\tcolor: #000000 !important;\n\t\t\t\t\t\t}\n\t\t\t\t\t\t.docsbot-user-chat-message {\n\t\t\t\t\t\t\tbackground-color: #0057C7;\n\t\t\t\t\t\t}\n\t\t\t\t\t\t.docsbot-chat-header-button {\n\t\t\t\t\t\t\tz-index:99999;\n\t\t\t\t\t\t}\n\t\t\t\t\t",e.getHelpHubPageElement=function(){return document.getElementById(e.selectors.helpHubPageID)},e.isOptedIn=function(){const t=e.getHelpHubPageElement();return t&&"1"===t.getAttribute("data-opted-in")},n.prototype.loadScript=function(){const t=this;return!0===t.scriptLoaded?Promise.resolve():(tribe.helpPage.BeaconManager.initStub(),new Promise(((e,n)=>{const o=document.createElement("script");o.src="https://beacon-v2.helpscout.net",o.async=!0,o.onload=()=>{t.scriptLoaded=!0,e()},o.onerror=()=>{n(new Error(helpHubSettings.errorMessages.helpScoutScriptLoadFailed))},document.head.appendChild(o)})))},n.prototype.initBeacon=function(){const t=this;return null!==t.initPromise||(t.initPromise=new Promise((n=>{tribe.helpPage.BeaconManager.init(t.beaconKey),tribe.helpPage.BeaconManager.config({display:{zIndex:1e6,style:"manual"},messaging:{chatEnabled:!0,previousMessagesEnabled:!0,contactForm:{showName:!0}}}),tribe.helpPage.BeaconManager.on("open",(()=>{e.toggleBlackout(!0)})),tribe.helpPage.BeaconManager.on("close",(()=>{e.toggleBlackout(!1)})),tribe.helpPage.BeaconManager.on("ready",(()=>{t.beaconReady=!0,null!==t.userIdentifiers&&t.userIdentifiers.name&&t.userIdentifiers.email&&tribe.helpPage.BeaconManager.identify({name:t.userIdentifiers.name,email:t.userIdentifiers.email}),n()}))}))),t.initPromise},n.prototype.openBeacon=function(){tribe.helpPage.BeaconManager.open()},e.helpScoutManager=null,e.setup=function(){const o=e.getHelpHubPageElement(),i=document.querySelector(e.selectors.optOutMessage),a=t(`#${e.selectors.docsbotWidget}`);if(!0!==e.isOptedIn())return null!==i&&i.classList.remove("hide"),a.length&&a.addClass("hide"),void(null!==o&&o.classList.add("blackout"));const s=helpHubSettings.helpScoutBeaconKey,c=helpHubSettings.userIdentifiers||null;e.helpScoutManager=new n(s,c),e.helpScoutManager.loadScript().then((()=>e.helpScoutManager.initBeacon())),e.initializeDocsBot(),a.length&&a.removeClass("hide"),null!==i&&i.classList.add("hide"),null!==o&&o.classList.remove("blackout")},e.openBeacon=function(){null!==e.helpScoutManager&&(tribe.helpPage.BeaconManager.close(),e.helpScoutManager.openBeacon())},e.observeElement=t=>new Promise((e=>{const n=document.querySelector(t);if(null!==n)return e(n);const o=new MutationObserver((n=>{const i=document.querySelector(t);null!==i&&(e(i),o.disconnect())}));o.observe(document.body,{childList:!0,subtree:!0})})),e.initializeDocsBot=function(){if(!0===window.DocsBotAIInitialized)return;window.DocsBotAIInitialized=!0;const n=t(`#${e.selectors.docsbotWidget}`);n.length&&n.removeClass("hide"),"function"!=typeof DocsBotAI.init&&(DocsBotAI.init=t=>new Promise(((n,o)=>{const i=document.createElement("script");i.type="text/javascript",i.async=!0,i.src="https://widget.docsbot.ai/chat.js";const a=document.getElementsByTagName("script")[0];a.parentNode.insertBefore(i,a),i.addEventListener("load",(()=>{Promise.all([window.DocsBotAI.mount({...t}),e.observeElement("#docsbotai-root")]).then(n).catch(o)})),i.addEventListener("error",(t=>{o(t.message)}))}))),DocsBotAI.init({id:helpHubSettings.docsbot_key,options:{customCSS:e.DocsBotAIcss},supportCallback:t=>{t.preventDefault(),e.toggleBlackout(!0),e.openBeacon()}})},e.toggleBlackout=function(t){const n=e.getHelpHubPageElement();null!==n&&n.classList.toggle("blackout",t)},t(e.setup),window.addEventListener("message",(t=>{if(t.origin!==window.location.origin)return;const{action:n,data:o}=t.data||{};"runScript"===n&&"openLivechat"===o&&"function"==typeof e.openBeacon&&e.openBeacon()}))})(jQuery,tribe.helpPage),window.tec=window.tec||{},window.tec.common=window.tec.common||{},window.tec.common.admin=window.tec.common.admin||{},window.tec.common.admin.helpHubIframe={};