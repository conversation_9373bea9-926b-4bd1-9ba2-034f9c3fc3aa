window.tribe.settings=window.tribe.settings||{},window.tribe.settings.fields=window.tribe.settings.fields||{},window.tribe.settings.fields.image={},function(e,i){"use strict";const t=e(document);i.frame=!1,i.selectors={imageFieldContainer:".tribe-field-image, .tribe-field-image_id",addImgLink:".tec-admin__settings-image-field-btn-add",removeImgLink:".tec-admin__settings-image-field-btn-remove",imgContainer:".tec-admin__settings-image-field-image-container",imgIdInput:".tec-admin__settings-image-field-input"},i.hideElements=function(e){const t=""!==e.find(i.selectors.imgIdInput).val();e.find(i.selectors.addImgLink).toggleClass("hidden",t),e.find(i.selectors.removeImgLink).toggleClass("hidden",!t),e.find(i.selectors.imgContainer).toggleClass("hidden",!t)},i.onImageSelect=function(e){const t=i.frame.state().get("selection").first().toJSON(),n=e.find(i.selectors.imgContainer);n.find("img").length>0?n.find("img").attr("src",t.url):n.html('<img src="'+t.url+'" />'),e.is("[data-image-id=1]")?e.find(i.selectors.imgIdInput).val(t.id):e.find(i.selectors.imgIdInput).val(t.url),i.hideElements(e)},i.addImage=function(t){t.preventDefault();const n=e(t.target).closest(i.selectors.imageFieldContainer);i.frame||(i.frame=wp.media({title:n.data("select-image-text"),button:{text:n.data("use-image-text")},multiple:!1})),i.frame.open(),i.frame.off("select").on("select",(function(){i.onImageSelect(n)}))},i.removeImage=function(t){t.preventDefault();const n=e(t.target).closest(i.selectors.imageFieldContainer);n.find(i.selectors.imgIdInput).val(""),n.find(i.selectors.imgContainer).html(""),i.hideElements(n)},i.bindEvents=function(){t.on("click",i.selectors.addImgLink,i.addImage),t.on("click",i.selectors.removeImgLink,i.removeImage)},i.init=function(){e(i.selectors.imageFieldContainer).each((function(t,n){i.hideElements(e(n))})),i.bindEvents()},e(i.init)}(jQuery,window.tribe.settings.fields.image),window.tec=window.tec||{},window.tec.common=window.tec.common||{},window.tec.common.adminImageField={};