tribe.events=tribe.events||{},tribe.events.admin=tribe.events.admin||{},tribe.events.admin.noticeInstall={},function(e,t,n){"use strict";const i=e(document);n.selectors={noticeDescription:".tec-admin__notice-install-content-description"},n.ready=function(){wp.hooks.addAction("stellarwp_installer_tec_error","tec/eventTicketsInstallerError",(function(t,i,s,o){const c=e(t);c.siblings(n.selectors.noticeDescription).html(o),c.remove()}))},i.ready(n.ready)}(jQuery,window.underscore||window._,tribe.events.admin.noticeInstall),window.tec=window.tec||{},window.tec.events=window.tec.events||{},window.tec.events.admin=window.tec.events.admin||{},window.tec.events.admin.noticeInstallEventTickets={};