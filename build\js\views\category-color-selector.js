tribe.events=tribe.events||{},tribe.events.categoryColors=tribe.events.categoryColors||{},tribe.events.categoryColors.categoryPicker=function(){"use strict";const e={picker:".tec-events-category-color-filter",dropdown:".tec-events-category-color-filter__dropdown",dropdownLabel:".tec-events-category-color-filter__dropdown-item label",checkbox:".tec-events-category-color-filter__checkbox",dropdownIcon:".tec-events-category-color-filter__dropdown-icon",dropdownVisible:"tec-events-category-color-filter__dropdown--visible",resetButton:".tec-events-category-color-filter__reset",pickerOpen:"tec-events-category-color-filter--open",pickerAlignRight:"tec-events-category-color-filter--align-right",dropdownClose:".tec-events-category-color-filter__dropdown-close",dataBound:"data-bound",childParentPairs:[{child:".tribe-events-calendar-list__event",parent:".tribe-events-calendar-list__event-row"},{child:".tribe-events-calendar-day__event",parent:".tribe-events-calendar-day__event"},{child:".tribe-events-calendar-month__calendar-event",parent:".tribe-events-calendar-month__calendar-event"},{child:".tribe-events-pro-summary__event",parent:".tribe-events-pro-summary__event"},{child:".tribe-events-pro-photo__event",parent:".tribe-events-pro-photo__event"},{child:".tribe-events-pro-week-grid__event",parent:".tribe-events-pro-week-grid__event"},{child:".tribe-events-pro-week-grid__multiday-event",parent:".tribe-events-pro-week-grid__multiday-event-wrapper"},{child:".tribe-events-calendar-month__multiday-event",parent:".tribe-events-calendar-month__multiday-event"},{child:".tribe-events-calendar-month-mobile-events__mobile-event",parent:".tribe-events-calendar-month-mobile-events__mobile-day"},{child:".tribe-events-calendar-month__multiday-event",parent:".tribe-events-calendar-month__day"},{child:".tribe-events-pro-week-mobile-events__event",parent:".tribe-events-pro-week-mobile-events__event"},{child:".tribe-events-pro-map__event-card-wrapper",parent:".tribe-events-pro-map__event-card-wrapper"}],filteredHide:"tec-category-filtered-hide",colorCircle:"tec-events-category-color-filter__color-circle",colorCircleDefault:"tec-events-category-color-filter__color-circle--default",colorCircleDefaultN:e=>`tec-events-category-color-filter__color-circle--default-${e}`,pickerContainer:".tec-events-category-color-filter"},t=new Set;let n=!1,r=null;const o=e=>document.querySelector(e),i=e=>document.querySelectorAll(e),c=t=>{const n=o(e.dropdown);if(n&&n.contains(t.target))return;t.stopPropagation();const r=t.currentTarget;n&&(d(n)?a(r,n):s(r,n))},s=(t,n)=>{n.classList.add(e.dropdownVisible),t.classList.add(e.pickerOpen),t.setAttribute("aria-expanded","true"),p(t,n)},a=(t,n)=>{n.classList.remove(e.dropdownVisible),t.classList.remove(e.pickerOpen),t.setAttribute("aria-expanded","false")},d=t=>t.classList.contains(e.dropdownVisible),l=t=>{t.target.closest(e.pickerContainer)||i(e.picker).forEach((t=>{const n=t.querySelector(e.dropdown);n&&d(n)&&a(t,n)}))},p=(e,t,n=!1)=>{if(!t.isConnected||!t.offsetParent)return;"static"===window.getComputedStyle(e).position&&(e.style.position="relative"),Object.assign(t.style,{left:"",right:"",top:"",position:"absolute"});const{left:r}=e.getBoundingClientRect(),o=window.innerWidth,i=e.offsetHeight;r>o/2?t.style.right="0px":t.style.left="0px",t.style.top=`${i}px`;const c=t.getBoundingClientRect().bottom,s=window.innerHeight- -8;if(c>s){const e=i-(c-s);t.style.top=`${e}px`}n||v(t)||window.requestAnimationFrame((()=>p(e,t,!0)))},v=e=>{const t=e.getBoundingClientRect();return t.top>=8&&t.left>=8&&t.bottom<=window.innerHeight-8&&t.right<=window.innerWidth-8},_=()=>{const n=document.getElementById("tec-category-color-legend");if(n)if(n.innerHTML="",t.size>0)Array.from(t).slice(0,5).forEach((t=>{const r=document.createElement("span");r.classList.add(e.colorCircle,`tribe_events_cat-${t}`),n.appendChild(r)}));else{const t=i(e.dropdownLabel);Array.from(t).slice(0,5).forEach((t=>{const r=t.dataset.category;if(r){const t=document.createElement("span");t.classList.add(e.colorCircle,`tribe_events_cat-${r}`),n.appendChild(t)}}))}},u=()=>{i(e.checkbox).forEach((e=>{e.checked=!1})),t.clear(),b(),_()},w=n=>{const r=n.target.closest(e.checkbox);if(!r)return;const o=r.closest("label");if(!o)return;const i=o.dataset.category;i&&(r.checked?t.add(i):t.delete(i),b(),_())},b=()=>{const n=[...t];e.childParentPairs.flatMap((e=>[...document.querySelectorAll(e.parent)])).forEach((t=>{let r=t;[...r.classList].some((e=>e.startsWith("tribe_events_cat-")))||(r=t.querySelector('[class*="tribe_events_cat-"]'));const o=!!r&&h(r,n);t.classList.toggle(e.filteredHide,n.length>0&&!o)}))},h=(e,t)=>{const n=[...e.classList].filter((e=>e.startsWith("tribe_events_cat-")));return t.some((e=>n.includes(`tribe_events_cat-${e}`)))},y=()=>{if(n)return;n=!0;const e=window.XMLHttpRequest.prototype.open;window.XMLHttpRequest.prototype.open=function(t,n,...r){return n.includes("/wp-json/tribe/views/v2/html")&&this.addEventListener("load",(function(){if(4===this.readyState&&200===this.status)try{setTimeout((()=>{f(),g(),_()}),50)}catch(e){f()}})),e.apply(this,[t,n,...r])}},g=()=>{i(e.checkbox).forEach((e=>{const n=e.closest("label"),r=n?.dataset.category;r&&(e.checked=t.has(r))})),b(),_()},f=(t=0)=>{t>5||window.requestAnimationFrame((()=>{const n=o(e.picker);n?(m(),k(n)||(L(),n.setAttribute(e.dataBound,"true"))):setTimeout((()=>f(t+1)),50)}))},m=()=>{const t=o(e.picker);t&&k(t)&&t.removeAttribute(e.dataBound)},k=t=>t.hasAttribute(e.dataBound),L=()=>{const t=o(e.picker),n=o(e.dropdownClose),i=o(e.resetButton);t&&(t.addEventListener("click",c),t.addEventListener("keydown",(e=>{("Enter"===e.key||" "===e.key)&&(e.preventDefault(),c(e))}))),document.addEventListener("click",l),document.addEventListener("keydown",(t=>{if("Escape"===t.key){const t=o(e.picker),n=o(e.dropdown);n&&d(n)&&(a(t,n),t?.focus())}}));const s=o(e.dropdown);s&&s.addEventListener("change",w),n&&n.addEventListener("click",(t=>{t.stopPropagation();const n=o(e.picker),r=o(e.dropdown);n&&r&&a(n,r)})),i&&i.addEventListener("click",u),r||(r=new window.MutationObserver(m),r.observe(document.body,{childList:!0,subtree:!0})),window.addEventListener("beforeunload",m)},C=()=>{y(),L(),_()};return document.addEventListener("DOMContentLoaded",C),{init:C,ensureBindings:f}}(),window.tec=window.tec||{},window.tec.events=window.tec.events||{},window.tec.events.views=window.tec.events.views||{},window.tec.events.views.categoryColorSelector={};