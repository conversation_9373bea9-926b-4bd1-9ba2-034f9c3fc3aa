window.tec=window.tec||{},window.tec.main=window.tec.main||{},window.tec.main.ece=window.tec.main.ece||{},function(e,n){const t=e(document);n.selectors={container:'[data-js="tribe-events-view"]',eventsInDay:".tribe-events-calendar-month__calendar-event a",eventsInToolTip:".tribe-events-tooltip-theme a",eventsInMobile:".tribe-events-calendar-month-mobile-events__mobile-event-title a",moreEventsLink:".tribe-events-calendar-month__more-events-link",moreEventsLinkMobile:".tribe-events-calendar-month-mobile-events__more-events-link"},n.openEventInNewTab=e=>{e.target.href&&(e.preventDefault(),window.open(e.target.href,"_blank"))},n.openMoreEventsLinkInNewTab=o=>{const i=t.find(o);i.length&&(e(n.selectors.container).find(o).off("click.tribeEvents",tribe.events.views.manager.onLinkClick),i.each(((t,o)=>{e(o).on("click.tribeEvents",n.openEventInNewTab)})))},n.refreshMoreEventsLinks=()=>{n.openMoreEventsLinkInNewTab(n.selectors.moreEventsLink),n.openMoreEventsLinkInNewTab(n.selectors.moreEventsLinkMobile)},n.ready=()=>{tribe?.events?.views?.manager?.onLinkClick?(t.on("click",n.selectors.eventsInDay,n.openEventInNewTab),t.on("click",n.selectors.eventsInToolTip,n.openEventInNewTab),t.on("click",n.selectors.eventsInMobile,n.openEventInNewTab),n.refreshMoreEventsLinks(),wp.hooks.addAction("tec.events.afterRequest","tec.events.ece",n.refreshMoreEventsLinks)):setTimeout((()=>{n.ready()}),100)},e(n.ready())}(jQuery,window.tec.main.ece),window.tec=window.tec||{},window.tec.events=window.tec.events||{},window.tec.events.calendarEmbeds=window.tec.events.calendarEmbeds||{},window.tec.events.calendarEmbeds.page={};