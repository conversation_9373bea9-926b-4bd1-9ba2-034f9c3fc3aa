(()=>{var t={9462:(t,e,i)=>{var o;!function(){"use strict";const i=["a[href]","area[href]","input:not([disabled])","select:not([disabled])","textarea:not([disabled])","button:not([disabled])","iframe","object","embed","[contenteditable]",'[tabindex]:not([tabindex^="-"])'];let n;const s=function(){const t=/(android)/i.test(navigator.userAgent),e=!!window.chrome,i="undefined"!=typeof InstallTrigger,o=document.documentMode,n=!o&&!!window.StyleMedia,s=!!navigator.userAgent.match(/(iPod|iPhone|iPad)/i),r=!!navigator.userAgent.match(/(iPod|iPhone)/i),d=!!window.opera||navigator.userAgent.indexOf(" OPR/")>=0;return{android:t,chrome:e,edge:n,firefox:i,ie:o,ios:s,iosMobile:r,opera:d,safari:Object.prototype.toString.call(window.HTMLElement).indexOf("Constructor")>0||!e&&!d&&"undefined"!==window.webkitAudioContext,os:navigator.platform}}();let r=0;const d=s.ie||s.firefox||s.chrome&&!s.edge?document.documentElement:document.body;function a(t){var e,i,o;this.options=(e={appendTarget:"",bodyLock:!0,closeButtonAriaLabel:"Close this dialog window",closeButtonClasses:"a11y-dialog__close-button",contentClasses:"a11y-dialog__content",effect:"none",effectSpeed:300,effectEasing:"ease-in-out",overlayClasses:"a11y-dialog__overlay",overlayClickCloses:!0,trigger:null,wrapperClasses:"a11y-dialog",ariaDescribedBy:"",ariaLabel:"",ariaLabelledBy:""},i=t,Object.keys(i).forEach((function(t){e[t]=i[t]})),e),this._rendered=!1,this._show=this.show.bind(this),this._hide=this.hide.bind(this),this._maintainFocus=this._maintainFocus.bind(this),this._bindKeypress=this._bindKeypress.bind(this),this.trigger=(o=this.options.trigger,"[object String]"===Object.prototype.toString.call(o)?h(this.options.trigger,!0,document,!0):this.options.trigger),this.node=null,this.trigger?(this._listeners={},this.create()):console.warn("Lookup for a11y target node failed.")}function c(t){const e=[];let i=t.length;for(;i--;e.unshift(t[i]));return e}function h(t,e,i,o){i||(i=document);const n=o?t:'[data-js="'+t+'"]';let s=i.querySelectorAll(n);return e&&(s=c(s)),s}function l(t){const e=u(t);e.length&&e[0].focus()}function u(t){return(e=i.join(","),o=t,c((o||document).querySelectorAll(e))).filter((function(t){return!!(t.offsetWidth||t.offsetHeight||t.getClientRects().length)}));var e,o}a.prototype.create=function(){return this.shown=!1,this.trigger.forEach(function(t){t.addEventListener("click",this._show)}.bind(this)),this._fire("create"),this},a.prototype.render=function(t){const e=h(this.trigger[0].dataset.content)[0];if(!e)return this;const i=this.options.ariaDescribedBy?'aria-describedby="'+this.options.ariaDescribedBy+'" ':"",o=this.options.ariaLabel?'aria-label="'+this.options.ariaLabel+'"':"",n=this.options.ariaLabelledBy?'aria-labelledby="'+this.options.ariaLabelledBy+'"':"",s=document.createElement("div");s.setAttribute("aria-hidden","true"),s.classList.add(this.options.wrapperClasses),s.innerHTML='<div data-js="a11y-overlay" tabindex="-1" class="'+this.options.overlayClasses+'"></div>\n  <div class="'+this.options.contentClasses+'" role="dialog" aria-modal="true" '+n+i+o+'>\n    <div role="document">\n      <button            data-js="a11y-close-button"           class="'+this.options.closeButtonClasses+'"            type="button"            aria-label="'+this.options.closeButtonAriaLabel+'"       ></button>\n      '+e.innerHTML+"    </div>\n  </div>";let r=this.trigger;var d,a;return this.options.appendTarget.length&&(r=document.querySelectorAll(this.options.appendTarget)[0]||this.trigger),d=s,(a=r).parentNode.insertBefore(d,a.nextElementSibling),this.node=s,this.overlay=h("a11y-overlay",!1,this.node)[0],this.closeButton=h("a11y-close-button",!1,this.node)[0],this.options.overlayClickCloses&&this.overlay.addEventListener("click",this._hide),this.closeButton.addEventListener("click",this._hide),this._rendered=!0,this._fire("render",t),this},a.prototype.show=function(t){return this.shown?this:(this._rendered||this.render(t),this._rendered?(this.shown=!0,this._applyOpenEffect(),this.node.setAttribute("aria-hidden","false"),this.options.bodyLock&&(r=d.scrollTop,document.body.classList.add("a11y-dialog__body-locked"),document.body.style.position="fixed",document.body.style.width="100%",document.body.style.marginTop="-"+r+"px"),n=document.activeElement,l(this.node),document.body.addEventListener("focus",this._maintainFocus,!0),document.addEventListener("keydown",this._bindKeypress),this._fire("show",t),this):this)},a.prototype.hide=function(t){return this.shown?(this.shown=!1,"none"===this.options.effect&&this.node.setAttribute("aria-hidden","true"),this._applyCloseEffect(),this.options.bodyLock&&(document.body.style.marginTop="",document.body.style.position="",document.body.style.width="",d.scrollTop=r,document.body.classList.remove("a11y-dialog__body-locked")),n&&n.focus(),document.body.removeEventListener("focus",this._maintainFocus,!0),document.removeEventListener("keydown",this._bindKeypress),this._fire("hide",t),this):this},a.prototype.destroy=function(){return this.hide(),this.trigger.forEach(function(t){t.removeEventListener("click",this._show)}.bind(this)),this._rendered&&(this.options.overlayClickCloses&&this.overlay.removeEventListener("click",this._hide),this.closeButton.removeEventListener("click",this._hide)),this._fire("destroy"),this._listeners={},this},a.prototype.on=function(t,e){return void 0===this._listeners[t]&&(this._listeners[t]=[]),this._listeners[t].push(e),this},a.prototype.off=function(t,e){const i=this._listeners[t].indexOf(e);return i>-1&&this._listeners[t].splice(i,1),this},a.prototype._fire=function(t,e){(this._listeners[t]||[]).forEach(function(t){t(this.node,e)}.bind(this))},a.prototype._bindKeypress=function(t){this.shown&&27===t.which&&(t.preventDefault(),this.hide()),this.shown&&9===t.which&&function(t,e){const i=u(t),o=i.indexOf(document.activeElement);e.shiftKey&&0===o?(i[i.length-1].focus(),e.preventDefault()):e.shiftKey||o!==i.length-1||(i[0].focus(),e.preventDefault())}(this.node,t)},a.prototype._maintainFocus=function(t){this.shown&&!this.node.contains(t.target)&&l(this.node)},a.prototype._applyOpenEffect=function(){const t=this;setTimeout((function(){t.node.classList.add("a11y-dialog--open")}),50),"fade"===this.options.effect&&(this.node.style.opacity="0",this.node.style.transition="opacity "+this.options.effectSpeed+"ms "+this.options.effectEasing,setTimeout((function(){t.node.style.opacity="1"}),50))},a.prototype._applyCloseEffect=function(){const t=this;this.node.classList.remove("a11y-dialog--open"),"fade"===this.options.effect?(this.node.style.opacity="0",setTimeout((function(){t.node.style.transition="",t.node.setAttribute("aria-hidden","true")}),this.options.effectSpeed)):"css"===this.options.effect&&setTimeout((function(){t.node.setAttribute("aria-hidden","true")}),this.options.effectSpeed)},void 0!==t.exports?t.exports=a:void 0===(o=function(){return a}.apply(e,[]))||(t.exports=o)}(void 0!==i.g?i.g:window)}},e={};function i(o){var n=e[o];if(void 0!==n)return n.exports;var s=e[o]={exports:{}};return t[o](s,s.exports,i),s.exports}i.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(t){if("object"==typeof window)return window}}();var o=i(9462);window.tec=window.tec||{},window.tec.common=window.tec.common||{},window.tec.common.tecA11yDialog=o})();