tribe.copyToClipboard=tribe.copyToClipboard||{},function(o,t){"use strict";t.selectors={copyButton:".tec-copy-to-clipboard"},t.setupCopyButton=function(){const c=new ClipboardJS(t.selectors.copyButton);o(document).on("click",t.selectors.copyButton,(function(o){o.preventDefault()})),c.on("success",(function(t){t.clearSelection();const c=o(o(t.trigger).data("notice-target"));c.html('<span class="optin-success">'+tribeCopyToClipboard.clipboard_copied_text+"<span>"),c.show(),window.setTimeout((function(){c.html(""),c.hide()}),2e3)})),c.on("error",(function(t){const c=o(o(t.trigger).data("notice-target"));c.html('<span class="optin-fail">'+tribeCopyToClipboard.clipboard_fail_text+"<span>"),c.show(),window.setTimeout((function(){c.html(""),c.hide()}),2e3)}))},t.setupCopyButton()}(jQuery,tribe.copyToClipboard),window.tec=window.tec||{},window.tec.common=window.tec.common||{},window.tec.common.utils=window.tec.common.utils||{},window.tec.common.utils.tecCopyToClipboard={};