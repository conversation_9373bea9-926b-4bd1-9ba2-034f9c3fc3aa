tribe.events=tribe.events||{},tribe.events.views=tribe.events.views||{},tribe.events.views.navigationScroll={},function(e,t){"use strict";const n=e(document),i=e(window);t.scrollUp=function(t,n,s,o){const c=e(t.target),r=i.scrollTop(),v=c.offset();.75*r>v.top&&i.scrollTop(v.top)},t.ready=function(){n.on("afterAjaxSuccess.tribeEvents",tribe.events.views.manager.selectors.container,t.scrollUp)},e(t.ready)}(jQuery,tribe.events.views.navigationScroll),window.tec=window.tec||{},window.tec.events=window.tec.events||{},window.tec.events.views=window.tec.events.views||{},window.tec.events.views.navigationScroll={};