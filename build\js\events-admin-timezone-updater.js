jQuery((function(e){if("object"==typeof tribe_timezone_update){var t=e(".tribe-events-timezone-update-msg"),i=tribe_timezone_update.failure_msg,n=tribe_timezone_update.check;tribe_timezone_update.continue&&function i(c){if("string"==typeof c.html&&t.html(c.html),0==c)return void o();if(!c.continue)return void window.location.assign(window.location);const d={action:"tribe_timezone_update",check:n};e.post(ajaxurl,d,i,"json").fail(o)}(tribe_timezone_update)}function o(){t.html("<p>"+i+"</p>")}})),window.tec=window.tec||{},window.tec.events=window.tec.events||{},window.tec.events.eventsAdminTimezoneUpdater={};