!function(e,n,d){"use strict";const t=e(document),i=e(window);d.selectors={dependent:".tribe-dependent",active:".tribe-active",dependency:".tribe-dependency",dependencyVerified:".tribe-dependency-verified",dependencyManualControl:"[data-dependency-manual-control]",fields:"input, select, textarea",advanced_fields:".select2-container",linked:".tribe-dependent-linked"},d.isNumeric=function(e){return!isNaN(parseFloat(e))&&isFinite(e)},d.constraintConditions={condition:(e,d)=>n.isArray(d)?-1!==d.indexOf(e):e==d,not_condition:(e,d)=>n.isArray(d)?-1===d.indexOf(e):e!=d,is_not_empty:e=>""!=e,is_empty:e=>""===e,is_numeric:e=>d.isNumeric(e),is_not_numeric:e=>!d.isNumeric(e),is_checked:(e,__,n)=>!(!n.is(":checkbox")&&!n.is(":radio"))&&n.is(":checked"),is_not_checked:(e,__,n)=>!(!n.is(":checkbox")&&!n.is(":radio")||n.is(":checked"))},d.verify=function(i){const o=e(this),c="#"+o.attr("id"),s=o.val();if(!c)return;if(o.is(":radio")){const n=e("[name='"+o.attr("name")+"']");n.not(d.selectors.linked).on("change",(function(){n.trigger("verify.dependency")})).addClass(d.selectors.linked.replace(".",""))}const a=t.find('[data-depends="'+c+'"]').not(".select2-container");0!==a.length&&(a.each((function(t,i){let c=e(i);if(c.is("[data-dependent-parent]")){const e=c.data("dependent-parent"),n=c.closest(e);if(0===n.length)return void console.warn("Dependency: `data-dependent-parent` has bad selector",c);c=n.find(i)}let a={condition:!!c.is("[data-condition]")&&c.data("condition"),not_condition:!!c.is("[data-condition-not]")&&c.data("conditionNot"),is_not_empty:c.data("conditionIsNotEmpty")||c.is("[data-condition-is-not-empty]")||c.data("conditionNotEmpty")||c.is("[data-condition-not-empty]"),is_empty:c.data("conditionIsEmpty")||c.is("[data-condition-is-empty]")||c.data("conditionEmpty")||c.is("[data-condition-empty]"),is_numeric:c.data("conditionIsNumeric")||c.is("[data-condition-is-numeric]")||c.data("conditionNumeric")||c.is("[data-condition-numeric]"),is_not_numeric:c.data("conditionIsNotNumeric")||c.is("[data-condition-is-not-numeric]"),is_checked:c.data("conditionIsChecked")||c.is("[data-condition-is-checked]")||c.data("conditionChecked")||c.is("[data-condition-checked]"),is_not_checked:c.data("conditionIsNotChecked")||c.is("[data-condition-is-not-checked]")||c.data("conditionNotChecked")||c.is("[data-condition-not-checked]")};const r=d.selectors.active.replace(".",""),l=c.is("[data-dependency-check-disabled]"),p=c.is("[data-dependency-always-visible]"),y=!l&&o.is(":disabled"),u=c.data("condition-relation")||"or";let f;a=n.pick(a,(function(e){return!1!==e})),f="or"===u?n.reduce(a,(function(e,n,t){return e||d.constraintConditions[t](s,n,o)}),!1):n.reduce(a,(function(e,n,t){return e&&d.constraintConditions[t](s,n,o)}),!0),f&&!y?(c.is(".tribe-dropdown, .tribe-ea-dropdown")?(c.select2TEC().data("select2").$container.addClass(r),c.select2TEC().data("select2").$container.is(":hidden")&&c.select2TEC().data("select2").$container.show()):(c.addClass(r),c.is(":hidden")&&c.show()),p&&c.filter(d.selectors.fields).prop("disabled",!1),c.find(d.selectors.fields).not(d.selectors.dependencyManualControl).prop("disabled",!1),void 0!==e().select2&&c.find(".tribe-dropdown, .tribe-ea-dropdown").select2TEC().prop("disabled",!1)):(c.removeClass(r),c.is(":visible")&&c.hide(),c.data("dependency-dont-disable")||c.find(d.selectors.fields).not(d.selectors.dependencyManualControl).prop("disabled",!0),void 0!==e().select2&&c.find(".tribe-dropdown, .tribe-ea-dropdown").select2TEC().prop("disabled",!0),c.is(".tribe-dropdown, .tribe-ea-dropdown")&&c.select2TEC().data("select2").$container.removeClass(r),p&&(c.addClass(r).show(),c.filter(d.selectors.fields).prop("disabled",!0),c.is(".tribe-dropdown, .tribe-ea-dropdown")&&c.select2TEC().data("select2").$container.addClass(r).show()));const h=c.find(d.selectors.dependency);h.length>0&&h.trigger("change")})),o.addClass(d.selectors.dependencyVerified.className()))},d.setup=function(n){const t=e(d.selectors.dependent);t.length&&t.dependency();const i=e(d.selectors.dependency);i.not(d.selectors.dependencyVerified).length&&i.trigger("verify.dependency")},e.fn.dependency=function(){return this.each((function(){const n=e(this),t=n.data("depends"),i=e(t);i.length&&(i.get(0).created||(i.addClass(d.selectors.dependency.replace(".","")).data("dependent",n),i.get(0).created=!0))}))},t.on("setup.dependency",d.setup),t.off("change.dependency verify.dependency",d.selectors.dependency),t.on({"verify.dependency":d.verify,"change.dependency":d.verify},d.selectors.dependency),e(d.setup),i.on("load",d.setup),e(document).on("widget-updated widget-added",d.setup)}(jQuery,window.underscore||window._,{}),window.tec=window.tec||{},window.tec.common=window.tec.common||{},window.tec.common.dependency={};