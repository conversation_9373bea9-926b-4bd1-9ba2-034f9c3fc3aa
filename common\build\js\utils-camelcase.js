window.tribe.utils=window.tribe.utils||{},function(t,e){"use strict";t.camelCase=function(t){return 0===(t=arguments.length>1?(t=e.map(arguments,(function(t){return t.trim()}))).filter(t,(function(t){return 0!==t.length})).join("-"):t.trim()).length?"":1===t.length?t.toLowerCase():/^[a-z0-9]+$/.test(t)?t:(t!==t.toLowerCase()&&(t=function(t){let e=!1,o=!1,n=!1;for(let r=0;r<t.length;r++){const s=t[r];e&&/[a-zA-Z]/.test(s)&&s.toUpperCase()===s?(t=t.substr(0,r)+"-"+t.substr(r),e=!1,n=o,o=!0,r++):o&&n&&/[a-zA-Z]/.test(s)&&s.toLowerCase()===s?(t=t.substr(0,r-1)+"-"+t.substr(r-1),n=o,o=!1,e=!0):(e=s.toLowerCase()===s,n=o,o=s.toUpperCase()===s)}return t}(t)),t.replace(/^[_.\- ]+/,"").toLowerCase().replace(/[_.\- ]+(\w|$)/g,(function(t,e){return e.toUpperCase()})))}}(window.tribe.utils,window.underscore||_),window.tec=window.tec||{},window.tec.common=window.tec.common||{},window.tec.common.utilsCamelcase={};