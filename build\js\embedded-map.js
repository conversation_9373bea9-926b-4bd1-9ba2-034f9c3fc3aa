"function"==typeof jQuery&&jQuery((function(e){let n,o,t,i,d,a;function s(){const i={zoom:parseInt(tribeEventsSingleMap.zoom),center:o,mapTypeId:google.maps.MapTypeId.ROADMAP};t.map=new google.maps.Map(n,i);const d={map:t.map,title:a,position:o};e("body").trigger("map-created.tribe",[t.map,n,i]),"undefined"!==tribeEventsSingleMap.pin_url&&tribeEventsSingleMap.pin_url&&(d.icon=tribeEventsSingleMap.pin_url),new google.maps.Marker(d)}"undefined"!=typeof tribeEventsSingleMap&&e.each(tribeEventsSingleMap.addresses,(function(e,p){n=document.getElementById("tribe-events-gmap-"+e),null!==n&&(t=void 0!==p?p:{},i=void 0!==p.address&&p.address,d=void 0!==p.coords&&p.coords,a=p.title,!1!==d?(o=new google.maps.LatLng(d[0],d[1]),s()):(new google.maps.Geocoder).geocode({address:i},(function(e,n){n==google.maps.GeocoderStatus.OK&&(o=e[0].geometry.location,s())})))}))})),window.tec=window.tec||{},window.tec.events=window.tec.events||{},window.tec.events.embeddedMap={};