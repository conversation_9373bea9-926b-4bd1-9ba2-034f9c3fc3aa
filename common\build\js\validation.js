window.tribe.validation={},dayjs.extend(window.dayjs_plugin_customParseFormat),function(t,e,i){"use strict";const a=e(document);t.selectors={item:".tribe-validation",fields:"input, select, textarea",submit:".tribe-validation-submit",submitButtons:'input[type="submit"], button[type="submit"]',error:".tribe-validation-error",valid:".tribe-validation-valid",notice:".tribe-notice-validation",noticeAfter:".wp-header-end",noticeFallback:".wrap > h1",noticeDismiss:".notice-dismiss"},t.conditions={isRequired:t=>""!=t,isGreaterThan(e,i,a){const n=t.parseCondition("isGreaterThan",e,i,a);return!1===n||n.constraint<n.value},isGreaterOrEqualTo(e,i,a){const n=t.parseCondition("isGreaterOrEqualTo",e,i,a);return!1===n||n.constraint<=n.value},isLessThan(e,i,a){const n=t.parseCondition("isLessThan",e,i,a);return!1===n||n.constraint>n.value},isLessOrEqualTo(e,i,a){const n=t.parseCondition("isLessOrEqualTo",e,i,a);return!1===n||n.constraint>=n.value},isEqualTo(e,i,a){const n=t.parseCondition("isEqualTo",e,i,a);return!1===n||n.constraint==n.value},isNotEqualTo(e,i,a){const n=t.parseCondition("isNotEqualTo",e,i,a);return!1===n||n.constraint!=n.value},matchRegExp:(t,e,i)=>null!==new RegExp(e,"g").exec(t),notMatchRegExp:(t,e,i)=>null===new RegExp(e,"g").exec(t)},t.parseType={datepicker(t,e,a){const n=["yyyy-mm-dd","m/d/yyyy","mm/dd/yyyy","d/m/yyyy","dd/mm/yyyy","m-d-yyyy","mm-dd-yyyy","d-m-yyyy","dd-mm-yyyy","yyyy.mm.dd","mm.dd.yyyy","dd.mm.yyyy"];let s=0;e.length&&e.attr("data-datepicker_format")?s=e.attr("data-datepicker_format"):i.isString(n[e])?s=n[e]:e.parents("[data-datepicker_format]").length&&(s=e.parents("[data-datepicker_format]").eq(0).data("datepicker_format")),void 0!==n[s]&&n[s]||(s=0);const r=n[s].toUpperCase();return dayjs(t,r).valueOf()},default:(t,i,a)=>(e.isNumeric(t)&&(t=parseFloat(t,10)),t)},t.parseCondition=function(a,n,s,r){let o=r.data("validationType"),l=null;const d={value:n,constraint:s};if(o||i.isFunction(t.parseType[o])||(o="default"),!e.isNumeric(s)){if(l=e(s),!l.length)return console.warn("Tribe Validation:","Invalid selector for",r,s),!1;if(l=l.not(":disabled"),!l.length)return!1;s=l.val()}return d.constraint=t.parseType[o](s,l,r),d.value=t.parseType[o](n,l,r),d},t.constraints={isRequired(t){let e=null;return e=t.data("required")||e,e=t.data("validationRequired")||e,e=t.data("validationIsRequired")||e,e=t.is("[required]")||e,e=t.is("[data-required]")||e,e=t.is("[data-validation-required]")||e,e=t.is("[data-validation-is-required]")||e,e},isGreaterThan(t){let e=null;return t.is("[data-validation-is-greater-than]")&&(e=t.data("validationIsGreaterThan")),e},isGreaterOrEqualTo(t){let e=null;return t.is("[data-validation-is-greater-or-equal-to]")&&(e=t.data("validationIsGreaterOrEqualTo")),e},isLessThan(t){let e=null;return t.is("[data-validation-is-less-than]")&&(e=t.data("validationIsLessThan")),e},isLessOrEqualTo(t){let e=null;return t.is("[data-validation-is-less-or-equal-to]")&&(e=t.data("validationIsLessOrEqualTo")),e},isEqualTo(t){let e=null;return t.is("[data-validation-is-equal-to]")&&(e=t.data("validationIsEqualTo")),e},isNotEqualTo(t){let e=null;return t.is("[data-validation-is-not-equal-to]")&&(e=t.data("validationIsNotEqualTo")),e},matchRegExp(t){let e=null;return t.is("[data-validation-match-regexp]")&&(e=t.data("validationMatchRegexp")),e},notMatchRegExp(t){let e=null;return t.is("[data-validation-not-match-regexp]")&&(e=t.data("validationNotMatchRegexp")),e}},t.fn=function(){return this.each(t.setup)},t.setup=function(i,n){const s=e(n);s.addClass(t.selectors.item.className()),s.find(t.selectors.submitButtons).addClass(t.selectors.submit.className()),s.on("submit.tribe",t.onSubmit),s.on("validation.tribe",t.onValidation),s.on("displayErrors.tribe",t.onDisplayErrors),a.on("click.tribe",t.selectors.submit,t.onClickSubmitButtons),a.on("click.tribe",t.selectors.noticeDismiss,t.onClickDismissNotice)},t.validate=function(i,a){const n=e(a);t.isValid(n)||(n.addClass(t.selectors.error.className()),n.one("change",t.onChangeFieldRemoveError))},t.isValid=function(e){const a=t.getConstraints(e);return i.isObject(a)?i.every(a):a},t.hasErrors=function(e){return 0!==e.find(t.selectors.error).not(":disabled").length},t.getConstraints=function(e){const a=!0;if(e.is(":disabled"))return a;let n=t.getConstraintsValue(e);const s=e.val();return i.isEmpty(n)?a:(n=i.mapObject(n,(function(i,a){return t.conditions[a](s,i,e)})),n)},t.getConstraintsValue=function(e){let a={};return e.is(":disabled")||(a=t.constraints,a=i.mapObject(a,(function(t){return t(e)})),a=i.pick(a,(function(t){return null!==t}))),a},t.getConstraintsFields=function(a){let n=t.getConstraintsValue(a);return n=i.mapObject(n,(function(t){let a=null;return i.isNumber(t)||i.isBoolean(t)||(a=e(t)),a})),n=i.pick(n,(function(t){return t instanceof jQuery})),n=i.values(n),n.unshift(a),n=e(n).map((function(){return this.get()})),n},t.onValidation=function(i){const a=e(this),n=a.find(t.selectors.fields);n.removeClass(t.selectors.error.className()),n.each(t.validate),0===a.find(t.selectors.error).not(":disabled").length?a.addClass(t.selectors.valid.className()):a.trigger("displayErrors.tribe")},t.onDisplayErrors=function(n){const s=e(this).find(t.selectors.error).not(":disabled"),r=e("<ul>"),o=e("<span>").addClass(t.selectors.noticeDismiss.className()),l=a.find(t.selectors.notice),d=e("<div>").addClass("notice notice-error is-dismissible tribe-notice").addClass(t.selectors.notice.className()).append(o);if(s.each((function(a,n){const s=e(n),o=s.data("validationError");if(i.isObject(o)){const e={},a=t.getConstraints(s,!1);i.each(o,(function(t,i){e[tribe.utils.camelCase(i)]=t})),i.each(a,(function(i,a){i||t.addErrorLine(e[a],s,r)}))}else t.addErrorLine(o,s,r)})),d.append(r),0===l.length){let e=a.find(t.selectors.noticeAfter);0===e.length&&(e=a.find(t.selectors.noticeFallback)),e.after(d)}else l.replaceWith(d)},t.addErrorLine=function(t,i,a){const n=e("<li>").text(t);n.data("validationField",i),i.data("validationNoticeItem",i),a.append(n)},t.onSubmit=function(i){const a=e(this);if(a.trigger("validation.tribe"),!a.is(t.selectors.valid))return i.preventDefault(),!1},t.onClickSubmitButtons=function(i){const a=e(this).parents(t.selectors.item);if(0===a.length)return;a.trigger("validation.tribe");const n=a.find(t.selectors.fields);n.off("invalid.tribe"),n.one("invalid.tribe",t.onInvalidField)},t.onInvalidField=function(i){const a=e(this),n=a.parents(t.selectors.item);return a.addClass(t.selectors.error.className()),n.trigger("displayErrors.tribe"),a.one("change",t.onChangeFieldRemoveError),i.preventDefault(),!1},t.onChangeFieldRemoveError=function(i){const a=e(this),n=t.getConstraintsFields(a);0!==n.filter(t.selectors.error).length&&n.removeClass(t.selectors.error.className())},t.onClickDismissNotice=function(i){e(this).parents(t.selectors.notice).remove()},t.onReady=function(i){e(t.selectors.item).validation()},e.fn.validation=t.fn,e(t.onReady)}(window.tribe.validation,jQuery,window.underscore||window._),window.tec=window.tec||{},window.tec.common=window.tec.common||{},window.tec.common.validation={};