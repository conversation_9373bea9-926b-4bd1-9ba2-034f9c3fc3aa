window.tribe_plugin_notices=window.tribe_plugin_notices||{},function(i,n){"use strict";n.init=function(){for(const n in tribe_plugin_notices){if(!tribe_plugin_notices.hasOwnProperty(n))continue;const t=i(tribe_plugin_notices[n].message_row_html);i('tr[data-plugin="'+n+'"].active').addClass("update").after(t)}},i((function(){"object"==typeof tribe_plugin_notices&&n.init()}))}(jQuery,window.tribe_plugin_notices),window.tec=window.tec||{},window.tec.common=window.tec.common||{},window.tec.common.pueNotices={};