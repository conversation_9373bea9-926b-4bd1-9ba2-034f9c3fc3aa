jQuery.fn.select2.amd.define("jquery.select2TEC",["jquery","jquery-mousewheel","./select2/core","./select2/defaults"],(function(e,t,n,o){if(null==e.fn.select2TEC){const t=["open","close","destroy"];e.fn.select2TEC=function(o){if("object"==typeof(o=o||{}))return this.each((function(){const t=e.extend(!0,{},o);new n(e(this),t)})),this;if("string"==typeof o){let n;const c=Array.prototype.slice.call(arguments,1);return this.each((function(){const t=e(this).data("select2");null==t&&window.console&&console.error&&console.error("The select2('"+o+"') method was called on an element that is not using Select2."),n=t[o].apply(t,c)})),e.inArray(o,t)>-1?this:n}throw new Error("Invalid arguments for Select2: "+o)}}return null==e.fn.select2TEC.defaults&&(e.fn.select2TEC.defaults=o),n})),jQuery.fn.select2.amd.require("jquery.select2TEC"),window.tec=window.tec||{},window.tec.common=window.tec.common||{},window.tec.common.select2After={};