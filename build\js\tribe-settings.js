jQuery((function(e){function i(){e(".google-embed-size input").prop("checked")?e(".google-embed-field").slideDown():e(".google-embed-field").slideUp()}i(),e(".google-embed-size input").on("change",i)})),function(e){"use strict";e((function(){if(-1!==[typeof pagenow,typeof typenow,typeof adminpage].indexOf("undefined"))return!1;const i=e("#tribe-field-toggle_blocks_editor"),o=e("#tribe-field-toggle_blocks_editor_hidden_field"),n=i.find("#tribe-blocks-editor-toggle-field"),t=o.find("#tribe-blocks-editor-toggle-hidden-field"),d=n.is(":checked");t.is(":checked")||d&&n.one("change",(function(){t.prop("checked",!0)}))}))}(jQuery),window.tec=window.tec||{},window.tec.events=window.tec.events||{},window.tec.events.tribeSettings={};