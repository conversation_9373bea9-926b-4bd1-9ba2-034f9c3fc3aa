(()=>{var e;e=window.commonIan||(window.commonIan={}),window.addEventListener("load",(function(t){e.icon=document.querySelector('[data-tec-ian-trigger="iconIan"]'),e.sidebar=document.querySelector('[data-tec-ian-trigger="sideIan"]'),e.notifications=document.querySelector('[data-tec-ian-trigger="notifications"]'),e.readAll=document.querySelector('[data-tec-ian-trigger="readAllIan"]'),e.optin=document.querySelector('[data-tec-ian-trigger="optinIan"]'),e.close=document.querySelector('[data-tec-ian-trigger="closeIan"]'),e.empty=document.querySelector('[data-tec-ian-trigger="emptyIan"]'),e.loader=document.querySelector('[data-tec-ian-trigger="loaderIan"]'),e.consent=e.notifications.dataset.consent,e.feed={read:[],unread:[]};const a=a=>{switch(i(),a.target.dataset.tecIanTrigger){case"iconIan":a.preventDefault(),a.stopPropagation(),e.sidebar.classList.toggle("is-hidden"),e.icon.classList.toggle("active");break;case"closeIan":a.preventDefault(),a.stopPropagation(),e.sidebar.classList.add("is-hidden"),e.icon.classList.remove("active");break;case"optinIan":a.preventDefault(),a.stopPropagation(),s();break;case"dismissIan":a.preventDefault(),a.stopPropagation(),c(a.target.dataset.id,a.target.dataset.slug);break;case"readIan":a.preventDefault(),a.stopPropagation(),t.stopPropagation(),l(a.target.dataset.id,a.target.dataset.slug);break;case"readAllIan":a.preventDefault(),a.stopPropagation(),p();break;default:console.log("e.composedPath",a.composedPath())}},n=()=>{const e=document.querySelector(".wrap .ian-header");let t={top:0,height:0};e&&(t=e.getBoundingClientRect());let a=document.getElementById("tribe-settings-tabs");a&&(a=window.innerWidth>500?a:document.querySelector(".tec-settings-header-wrap"),t=a.getBoundingClientRect());const n=document.getElementById("tec-admin-page-header");return n&&(t=n.getBoundingClientRect()),t.top+t.height},i=()=>{const t=n();e.sidebar.style.top=`${t}px`};let d=!1;const o=()=>{const t=window.innerWidth>782?32:window.innerWidth>600?46:0;window.scrollY,n()<=t?e.sidebar.style.top=t+"px":i(),d=!1},s=async()=>{e.optin.classList.add("disable"),e.loader.classList.remove("is-hidden");const t=new FormData;t.append("action","ian_optin"),t.append("nonce",e.nonce);try{(await fetch(e.ajaxUrl,{method:"POST",credentials:"same-origin",body:t}).then((e=>e.json()))).success&&r()}catch(e){console.error("Error during opt-in:",e)}finally{e.optin.classList.remove("disable"),e.loader.classList.add("is-hidden")}},r=async t=>{e.notifications.classList.remove("is-hidden"),t||e.loader.classList.remove("is-hidden");const a=new FormData;a.append("action","ian_get_feed"),a.append("nonce",e.nonce),a.append("plugin",g());try{const t=await fetch(e.ajaxUrl,{method:"POST",credentials:"same-origin",body:a}).then((e=>e.json()));if(e.loader.classList.add("is-hidden"),t.success)if(e.optin&&e.optin.remove(),0===t.data.length)e.feed={read:[],unread:[]},e.notifications.classList.add("is-hidden"),e.empty.classList.remove("is-hidden");else{let a="",n="";Object.entries(t.data).forEach((([e,t])=>{t.read?(a+=t.html,commonIan.feed.read.push(t)):(n+=t.html,commonIan.feed.unread.push(t))}));const i=`<div class="ian-sidebar__separator"><div>${e.readTxt}</div><span></span></div>`;e.notifications.innerHTML=n+i+a}m()}catch(e){console.error("Error fetching Ian feed:",e)}finally{e.loader.classList.add("is-hidden")}},c=async(t,a)=>{e.loader.classList.remove("is-hidden");const n=document.getElementById(`notification_${t}`);n.classList.add("fade-out");const i=new FormData;i.append("action","ian_dismiss"),i.append("slug",a),i.append("id",t),i.append("nonce",n.dataset.nonce);try{const a=await fetch(e.ajaxUrl,{method:"POST",credentials:"same-origin",body:i}).then((e=>e.json()));if(e.loader.classList.add("is-hidden"),a.success){n.remove();const{read:a,unread:i}=e.feed,d=e=>e.filter((e=>e.id!==parseInt(t)));e.feed.read=d(a),e.feed.unread=d(i),m()}else console.error("Failed to dismiss notification:",a.message||"Unknown error")}catch(e){console.error("Error dismissing notification:",e)}finally{e.loader.classList.add("is-hidden")}},l=async(t,a)=>{e.reading=t,e.loader.classList.remove("is-hidden");const n=document.getElementById(`notification_${t}`);n.classList.add("fade-out");const i=new FormData;i.append("action","ian_read"),i.append("slug",a),i.append("id",t),i.append("nonce",n.dataset.nonce);try{const a=await fetch(e.ajaxUrl,{method:"POST",credentials:"same-origin",body:i}).then((e=>e.json()));e.loader.classList.add("is-hidden"),a.success?(e.feed.read.unshift(e.feed.unread.find((e=>e.id===parseInt(t)))),e.feed.unread=e.feed.unread.filter((e=>e.id!==parseInt(t))),m(),document.querySelector(".ian-sidebar__separator").insertAdjacentElement("afterend",n),n.querySelector(".ian-sidebar__notification-link--right").remove(),n.classList.remove("fade-out")):console.error("Failed to read notification:",a.message||"Unknown error")}catch(e){console.error("Error reading notification:",e)}finally{e.loader.classList.add("is-hidden")}},p=async()=>{e.loader.classList.remove("is-hidden");const t=new FormData;t.append("action","ian_read_all"),t.append("nonce",e.nonce),t.append("unread",JSON.stringify(e.feed.unread.map((e=>e.slug))));try{const a=await fetch(e.ajaxUrl,{method:"POST",credentials:"same-origin",body:t}).then((e=>e.json()));if(e.loader.classList.add("is-hidden"),a.success){e.feed.read=[...e.feed.read,...e.feed.unread],e.feed.unread=[];const t=e.feed.read.map((e=>e.html)).join(""),a=`<div class="ian-sidebar__separator"><div>${e.readTxt}</div><span></span></div>`;e.notifications.innerHTML=a+t,document.querySelectorAll(".ian-sidebar__notification-link--right").forEach((e=>e.remove())),m()}else console.error("Failed to read all notifications:",a.message||"Unknown error")}catch(e){console.error("Error reading all notifications:",e)}finally{e.loader.classList.add("is-hidden")}},m=()=>{const t=e.feed.read.length>0,a=e.feed.unread.length>0,n=!a&&!t;e.icon.classList.toggle("unread",a),e.readAll.classList.toggle("is-hidden",!a),e.notifications.classList.toggle("is-hidden",n),e.empty.classList.toggle("is-hidden",!n),n||document.querySelector(".ian-sidebar__separator").classList.toggle("is-hidden",!t)},g=()=>document.body.classList.contains("tickets_page_tec-tickets-settings")?"et":"tec";var u;(()=>{document.querySelectorAll(".edit-php.post-type-tribe_events h1, .post-php.post-type-tribe_events h1").forEach((e=>{const t=e.nextElementSibling;if(t){const a=document.createElement("div");a.className="ian-header";const n=document.createElement("div");n.className="ian-inner-wrapper";const i=document.createElement("div");i.className="ian-client",i.setAttribute("data-tec-ian-trigger","iconIan"),e.parentNode.insertBefore(a,e),a.appendChild(n),n.appendChild(e),n.appendChild(t),n.appendChild(i)}}));const t=document.querySelector(".tec-settings-header-wrap");t&&t.insertAdjacentElement("afterend",e.sidebar)})(),e.icon=document.querySelector('[data-tec-ian-trigger="iconIan"]'),e.notifications=document.querySelector('[data-tec-ian-trigger="notifications"]'),e.readAll=document.querySelector('[data-tec-ian-trigger="readAllIan"]'),e.optin=document.querySelector('[data-tec-ian-trigger="optinIan"]'),e.close=document.querySelector('[data-tec-ian-trigger="closeIan"]'),e.empty=document.querySelector('[data-tec-ian-trigger="emptyIan"]'),e.loader=document.querySelector('[data-tec-ian-trigger="loaderIan"]'),e.consent=null!==(u=e.notifications?.dataset?.consent)&&void 0!==u?u:null,i(),document.querySelectorAll("[data-tec-ian-trigger]").forEach((e=>e.addEventListener("click",a))),document.addEventListener("click",(t=>{t.composedPath().includes(e.sidebar)||t.composedPath().includes(e.icon)||(e.sidebar&&e.sidebar.classList.add("is-hidden"),e.icon&&e.icon.classList.remove("active"))})),document.addEventListener("keydown",(t=>{(["Escape","Esc"].includes(t.key)||27===t.keyCode)&&(e.sidebar&&e.sidebar.classList.add("is-hidden"),e.icon&&e.icon.classList.remove("active"),i())})),window.addEventListener("resize",i),window.addEventListener("scroll",(()=>{d||(requestAnimationFrame(o),d=!0)})),"true"==e.consent&&r(!0)})),window.tec=window.tec||{},window.tec.common=window.tec.common||{},window.tec.common.ianClient={}})();