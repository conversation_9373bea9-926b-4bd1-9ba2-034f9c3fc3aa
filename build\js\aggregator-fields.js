window.tribe_aggregator=window.tribe_aggregator||{},function(e,t,i){"use strict";i.localized=window.tribe_aggregator_data||{},i.fields={selector:{container:".tribe-ea",form:".tribe-ea-form",help:".tribe-ea-help",fields:".tribe-ea-field",dropdown:".tribe-ea-dropdown",origin_field:"#tribe-ea-field-origin",field_url_source:"#tribe-ea-field-url_source",eventbrite_url_source:"#tribe-ea-field-eventbrite_source",post_status:".tribe-ea-field-post_status",import_type_field:".tribe-import-type",media_button:".tribe-ea-media_button",datepicker:".tribe-datepicker",save_credentials_button:".enter-credentials .tribe-save",preview_container:".tribe-preview-container",preview_button:".tribe-preview:visible",refine_filters:".tribe-refine-filters",clear_filters_button:".tribe-clear-filters",finalize_button:".tribe-finalize",cancel_button:".tribe-cancel",schedule_delete_link:".tribe-ea-tab-scheduled a.submitdelete",tab_new:".tribe-ea-tab-new",action:"#tribe-action",view_filters:".tribe-view-filters"},media:{},$:{},construct:{},events:{},import_id:null,result_fetch_count:0,max_result_fetch_count:15,polling_frequency_index:0,polling_frequencies:[500,1e3,5e3,2e4],eventbrite:{refineControls:".tribe-refine-filters.eventbrite, .tribe-refine-filters.eventbrite .tribe-refine",refineControlsHideMap:{event:"tr.tribe-refine-filters",organizer:""},detect_type:function(e){if(!i.localized.source_origin_regexp.eventbrite)return null;var r=i.localized.source_origin_regexp.eventbrite,l={event:r+"e/[A-z0-9_-]+",organizer:r+"o/[A-z0-9_-]+"},a=void 0;return t.each(l,(function(t,i){null!==new RegExp(t,"g").exec(e)&&(a=i)})),a}}},i.fields.init=function(){if(i.fields.$.container=e(i.fields.selector.container),i.fields.$.form=e(i.fields.selector.form),i.fields.$.action=e(i.fields.selector.action),i.fields.$.fields=i.fields.$.container.find(i.fields.selector.fields),i.fields.$.preview_container=e(i.fields.selector.preview_container),i.fields.origin=e("#tribe-ea-field-origin"),i.fields.importType=e("#tribe-ea-field-url_import_type"),i.fields.urlImport={startDate:e("#tribe-ea-field-url_start"),originalMinDate:function(){return e("#tribe-ea-field-url_start").datepicker("option","minDate")||""}},e.each(i.fields.construct,(function(e,t){t(i.fields.$.fields)})),void 0!==typeof tribe_ev||void 0!==typeof tribe_ev.state){var r=e(document.getElementById("eventDetails"));r.data("datepicker_format")&&(tribe_ev.state.datepicker_format=r.data("datepicker_format"))}e(document).on("keypress",i.fields.selector.fields,i.fields.events.trigger_field_change).on("click",i.fields.selector.save_credentials_button,i.fields.events.trigger_save_credentials).on("click",i.fields.selector.clear_filters_button,i.fields.clear_filters).on("click",i.fields.selector.finalize_button,i.fields.finalize_manual_import).on("click",i.fields.selector.preview_button,i.fields.preview_import).on("click",i.fields.selector.cancel_button,i.fields.events.cancel_edit).on("click",i.fields.selector.schedule_delete_link,i.fields.events.verify_schedule_delete).on("click",i.fields.selector.view_filters,i.fields.events.toggle_view_filters).on("blur",i.fields.selector.datepicker,i.fields.date_helper).on("submit",i.fields.selector.tab_new,i.fields.events.suppress_submission).on("change",i.fields.selector.import_type_field,(function(){i.fields.reset_preview();var t=e(this),r=e(this).next(i.fields.selector.fields),l=t.val();r.val("schedule"===l?"daily":"").trigger("change"),i.fields.$.form.attr("data-type",l),i.fields.maybeLimitUrlStartDate()})).on("change",i.fields.selector.origin_field,(function(){var t=e(this),r=(e(this).data("select2"),t.val());i.fields.$.form.attr("data-origin",r),i.fields.reset_preview(),e(".tribe-bumpdown-active").removeClass("tribe-bumpdown-active"),e(".tribe-bumpdown:visible").hide(),"redirect"===e(this).val()&&(window.open("https://theeventscalendar.com/wordpress-event-aggregator/?utm_source=importoptions&utm_medium=plugin-tec&utm_campaign=in-app","_blank"),location.reload()),""!==r&&e(i.fields.selector.post_status).val(i.localized.default_settings[r].post_status).trigger("change"),i.fields.maybeLimitUrlStartDate()})).on("change",i.fields.selector.eventbrite_url_source,(function(t){e(i.fields.eventbrite.refineControls).show();var r=i.fields.eventbrite.detect_type(e("#tribe-ea-field-eventbrite_source").val());if(r){var l=i.fields.eventbrite.refineControlsHideMap[r];l&&e(l).hide()}})).on("change",i.fields.selector.field_url_source,(function(r){var l=e(this),a=(e(this).data("select2"),l.val()),s=null;if(a&&(t.each(i.localized.source_origin_regexp,(function(e,t){null!==new RegExp(e,"g").exec(a)&&(s=t)})),null!=s)){var n=e(i.fields.selector.origin_field);if(n.find('option[value="'+s+'"]').length){var d=e("#tribe-ea-field-url_import_type"),o=d.val(),c=null;"schedule"===o&&(c=e("#tribe-ea-field-url_import_frequency").val()),d.val(""),n.val(s).trigger("change"),e("#tribe-ea-field-"+s+"_import_type").val(o).trigger("change"),"schedule"===o&&e("#tribe-ea-field-"+s+"_import_frequency").val(c).trigger("change"),"eventbrite"===s&&(e("#tribe-ea-field-"+s+"_source_type_url").trigger("click"),e("#tribe-ea-field-"+s+"_import_source").val("source_type_url").trigger("change")),e("#tribe-ea-field-"+s+"_source").val(a).trigger("change")}}})),e(".tribe-dependency").trigger("change"),tribe_timepickers.setup_timepickers(e(tribe_timepickers.selector.timepicker)),"edit"===i.fields.$.action.val()&&(i.fields.$.form.addClass("edit-form"),e(i.fields.selector.finalize_button).html(i.localized.l10n.edit_save)),"object"==typeof window.tribe_aggregator_save&&e(document).trigger("tribe_aggregator_init_notice")},i.fields.preview_import=function(t){t.preventDefault();var r=e(".tribe-ea-form.tribe-validation");if(i.fields.reset_post_status(),r.trigger("validation.tribe"),!tribe.validation.hasErrors(r)){i.fields.reset_polling_counter(),e(".tribe-fetch-warning-message").html("");var l=e("#tribe-post_id");l.data("value",l.val()),l.val("");var a=e("#tribe-import_id");a.data("value",a.val()),a.val("");var s=e(i.fields.selector.preview_button),n=(r=s.closest("form")).serialize();l.val(l.data("value")),a.val(l.data("value")),i.fields.$.preview_container.addClass("tribe-fetching").removeClass("tribe-fetch-error"),i.fields.$.form.removeClass("show-data"),s.prop("disabled",!0);var d=e(".dataTable").data("table");void 0!==d&&d.clear().draw(),"edit"===i.fields.$.action.val()?i.fields.preview_save_import(n):i.fields.create_import(n)}},i.fields.reset_post_status=function(){var t=e(i.fields.selector.origin_field),r=0===t.length?"":t.val();""!==r&&e(i.fields.selector.post_status).val(i.localized.default_settings[r].post_status).trigger("change")},i.fields.reset_polling_counter=function(){i.fields.polling_frequency_index=0,i.fields.result_fetch_count=0},i.fields.reset_form=function(){i.fields.$.fields.val("").trigger("change"),e('[id$="import_frequency"]').val("daily").trigger("change"),i.fields.$.form.removeClass("show-data")},i.fields.reset_preview=function(){i.fields.$.form.removeClass("show-data"),e(".tribe-fetched, .tribe-fetching, .tribe-fetch-error").removeClass("tribe-fetched tribe-fetching tribe-fetch-error")},i.fields.clear_filters=function(){e(i.fields.selector.refine_filters).find("input, select").val("").trigger("change")},i.fields.preview_save_import=function(t){e.ajax({type:"POST",url:ajaxurl+"?action=tribe_aggregator_preview_import",data:t,dataType:"json"}).done(i.fields.handle_preview_create_results)},i.fields.create_import=function(t){e.ajax({type:"POST",url:ajaxurl+"?action=tribe_aggregator_create_import",data:t,dataType:"json"}).done(i.fields.handle_preview_create_results)},i.fields.handle_preview_create_results=function(r){if(!r.success){var l=r.data;return t.isString(l)||(l=l.message),void i.fields.display_fetch_error(["<b>",i.localized.l10n.preview_fetch_error_prefix,"</b>"," "+l].join(" "))}if(i.fields.import_id=r.data.data.import_id,e("#tribe-import_id").val(i.fields.import_id),void 0!==r.data.data.items)return i.fields.init_datatable(r.data.data),void i.fields.$.preview_container.removeClass("tribe-fetching").addClass("tribe-fetched");i.fields.$.container.find(".spinner-message").html(i.localized.l10n.preview_polling[0]),setTimeout(i.fields.poll_for_results,i.fields.polling_frequencies[i.fields.polling_frequency_index])},i.fields.poll_for_results=function(){i.fields.result_fetch_count++;const t=["action=tribe_aggregator_fetch_import",`import_id=${i.fields.import_id}`,`tribe_aggregator_nonce=${i.localized.nonce}`];e.ajax({type:"GET",url:`${ajaxurl}?`+t.join("&"),dataType:"json"}).done((function(t){if(void 0!==t.data.warning&&t.data.warning){var r=t.data.warning;i.fields.display_fetch_warning(["<b>",i.localized.l10n.preview_fetch_warning_prefix,"</b>"," "+r].join(" "))}var l;if(!t.success)return void 0!==t.data.message?l=t.data.message:void 0!==t.data[0].message&&(l=t.data[0].message),void i.fields.display_fetch_error(["<b>",i.localized.l10n.preview_fetch_error_prefix,"</b>"," "+l].join(" "));"error"===t.data.status?i.fields.display_fetch_error(t.data.message):"success"!==t.data.status?(i.fields.result_fetch_count>i.fields.max_result_fetch_count&&(i.fields.polling_frequency_index++,i.fields.$.container.find(".spinner-message").html(i.localized.l10n.preview_polling[i.fields.polling_frequency_index]),i.fields.result_fetch_count=0),void 0===i.fields.polling_frequencies[i.fields.polling_frequency_index]?i.fields.display_fetch_error(i.localized.l10n.preview_timeout):setTimeout(i.fields.poll_for_results,i.fields.polling_frequencies[i.fields.polling_frequency_index])):(t.data.data.items=t.data.data.events,i.fields.init_datatable(t.data.data),i.fields.$.preview_container.removeClass("tribe-fetching").addClass("tribe-fetched"),e(i.fields.selector.preview_button).prop("disabled",!1))}))},i.fields.init_datatable=function(t){var r=!1,l="csv"===(x=e(i.fields.selector.origin_field).val()),a=e('[id$="import_type"]:visible'),s="manual";if(void 0!==i.localized.default_settings[x])for(var n in i.localized.default_settings[x])i.localized.default_settings[x].hasOwnProperty(n)&&e("#tribe-ea-field-"+n).val(i.localized.default_settings[x][n]).trigger("change");if(a.length&&(s=e("#"+a.first().attr("id").replace("s2id_","")).val()),"manual"!==s||t.items.length){a.length&&"manual"!==s||(r=!0);var d=i.fields.$.preview_container.find(".data-container table"),o=[];for(var c in t.items){var f=t.items[c];f.checkbox=r?'<input type="checkbox">':"",f.all_day?f.start_time=i.localized.l10n.all_day:(void 0!==f.start_meridian&&f.start_meridian||(parseInt(f.start_hour,10)>11?f.start_meridian=i.localized.l10n.pm:f.start_meridian=i.localized.l10n.am),f.start_hour>12&&(f.start_hour=f.start_hour-12),f.start_time=(0===parseInt(f.start_hour,10)?12:f.start_hour)+":"+("00"+f.start_minute).slice(-2),f.start_time+=" "+f.start_meridian),o.push(f)}r&&!l?d.addClass("display-checkboxes"):d.removeClass("display-checkboxes"),i.fields.$.form.addClass("show-data");var _,u={lengthMenu:[[5,10,25,50,-1],[5,10,25,50,tribe_l10n_datatables.pagination.all]],order:[[1,"asc"]],columnDefs:[{cellType:"th",className:"check-column",orderable:!1,targets:0}],data:o};if(void 0!==t.columns){u.columns=[{data:"checkbox"}];var p=d.find("thead tr"),v=d.find("tfoot tr"),g=e({}),m="",b="";if(p.find("th:first").nextAll().remove(),v.find("th:first").nextAll().remove(),l){var h=d.closest(".data-container");d.closest(".data-container").addClass("csv-data"),h.find(".tribe-preview-message .tribe-csv-filename").html(e("#tribe-ea-field-csv_file_name").text()),p.closest("thead").prepend('<tr class="tribe-column-map"><th scope="row" class="check-column column-cb"></th></tr>'),g=e(".tribe-column-map"),b=(b=e("#tribe-ea-field-csv_content_type").val()).replace("tribe_",""),m=e("#tribe-csv-column-map-"+b).html()}var w=0;for(c in t.columns){if(u.columns.push({data:t.columns[c]}),p.append('<th scope="col">'+t.columns[c]+"</th>"),v.append('<th scope="col">'+t.columns[c]+"</th>"),l){var y=t.columns[c].toLowerCase().replace(/^\s+|\s+$/g,"").replace(/\s/g,"_").replace(/[^a-z0-9_]/g,"");g.append('<th scope="col">'+m.replace('name="column_map[]"','name="aggregator[column_map]['+w+']" id="column-'+w+'"')+"</th>");var z=g.find("#column-"+w);void 0!==i.localized.csv_column_mapping[b][w]&&(y=i.localized.csv_column_mapping[b][w]),z.find('option[value="'+y+'"]').prop("selected",!0)}w++}u.scrollX=!0}else u.columns=[{data:"checkbox"},{data:"start_date"},{data:"start_time"},{data:"end_date"},{data:"title"}],u.autoWidth=!1;d.tribeDataTable(u),i.fields.wrap_cell_content(),d.on("select.dt",i.fields.events.twiddle_finalize_button_text).on("deselect.dt",i.fields.events.twiddle_finalize_button_text).on("draw.dt",i.fields.wrap_cell_content),"new"===i.fields.$.action.val()&&(_="manual"===s&&l?i.localized.l10n.import_all_no_number:"manual"===s?i.localized.l10n.import_all.replace("%d",o.length):i.localized.l10n.create_schedule),e(i.fields.selector.finalize_button).html(_)}else{var x=t.origin,k=void 0!==i.localized.l10n[x]&&void 0!==i.localized.l10n[x].no_results?i.localized.l10n[x].no_results:i.localized.l10n.no_results;i.fields.display_fetch_error(k)}},i.fields.wrap_cell_content=function(){e(".dataTable").find("tbody td").each((function(){var t=e(this);t.html('<div class="tribe-td-height-limit">'+t.html()+"</div>")}))},i.fields.display_fetch_error=function(t){var r=e(".tribe-fetch-error-message");i.fields.$.preview_container.removeClass("tribe-fetching").addClass("tribe-fetch-error"),r.html(""),i.fields.display_error(r,t),e(i.fields.selector.preview_button).prop("disabled",!1)},i.fields.display_fetch_warning=function(t){var r=e(".tribe-fetch-warning-message");i.fields.$.preview_container.removeClass("tribe-fetching").addClass("tribe-fetch-warning"),r.html(""),i.fields.display_warning(r,t)},i.fields.display_error=function(e,t){e.prepend(['<div class="notice notice-error">',"<p>",t,"</p>","</div>"].join(""))},i.fields.display_warning=function(e,t){e.prepend(['<div class="notice notice-warning">',"<p>",t,"</p>","</div>"].join(""))},i.fields.display_success=function(e,t){e.prepend(['<div class="notice notice-success">',"<p>",t,"</p>","</div>"].join(""))},i.fields.save_credentials=function(t){var r=t.find(".tribe-fieldset").find("input").serialize();r+=`&tribe_aggregator_nonce=${i.localized.nonce}`;var l=ajaxurl+"?action=tribe_aggregator_save_credentials";e.post(l,r).done((function(e){e.success&&(t.addClass("credentials-entered"),t.find('[name="has-credentials"]').val(1).trigger("change"))}))},i.fields.finalize_manual_import=function(){var t=e("#tribe-ea-field-origin").val(),r=e(".dataTable"),l=window.tribe_data_table;if(r.hasClass("display-checkboxes")){var a=l.rows({selected:!0});if(a[0].length||(a=l.rows()),!a[0].length)return void i.fields.display_error(e(".tribe-finalize-container"),i.localized.l10n.events_required_for_manual_submit);var s=a.data(),n=[],d=null;if("meetup"===t?d="meetup_id":"eventbrite"===t?d="eventbrite_id":"ical"===t||"ics"===t||"gcal"===t?d="uid":"url"===t&&(d="id"),null!==d){for(var o in s)isNaN(o)||void 0!==s[o][d]&&n.push(s[o][d]);e("#tribe-selected-rows").text(JSON.stringify(n))}else e("#tribe-selected-rows").text("all")}else e("#tribe-selected-rows").text("all");e(".dataTables_scrollBody").find('[name^="aggregator[column_map]"]').remove(),i.fields.$.form.trigger("submit")},i.fields.search_id=function(e){var t=null;return void 0!==e.id?t=e.id:void 0!==e.ID?t=e.ID:void 0!==e.value&&(t=e.value),null==e?null:t},i.fields.construct.dropdown=function(t){var i=function(t){var i=e(t.element);return"string"==typeof i.data("subtitle")&&(t.text=t.text+'<br><span class="tribe-dropdown-subtitle">'+i.data("subtitle")+"</span>"),t.text},r={formatResult:i,formatSelection:i};return tribe_dropdowns.dropdown(t.filter(".tribe-ea-dropdown"),r),t},i.fields.construct.media_button=function(t){var r=t.filter(i.fields.selector.media_button);return"undefined"!=typeof wp&&wp.media&&wp.media.editor?(r.each((function(){var t=e(this),r=t.data("input"),l=e("#"+r),a=e("#"+r+"_name"),s=i.fields.media[r]=wp.media({title:t.data("mediaTitle"),library:{type:t.data("mimeType")},multiple:!1});s.on("select",(function(){var e=s.state().get("selection");e&&e.each((function(e){l.data({id:e.attributes.id,text:e.attributes.title}),l.val(e.attributes.id),l.trigger("change"),a.html(e.attributes.filename),a.attr("title",e.attributes.filename)}))}))})),i.fields.$.container.on("click",i.fields.selector.media_button,(function(t){if(t.preventDefault(),e(this).is(":visible")){var r=e(this).data("input");return i.fields.media[r].open(r),!1}})),r):r},i.fields.events.trigger_field_change=function(){e(this).trigger("change")},i.fields.events.trigger_save_credentials=function(){i.fields.save_credentials(e(this).closest(".enter-credentials"))},i.fields.events.suppress_submission=function(t){if(e("#tribe-ea-field-origin").val(),e("#tribe-selected-rows").val().length)return!0;t.preventDefault()},i.fields.events.twiddle_finalize_button_text=function(t,r){if("new"===i.fields.$.action.val()){var l=r.rows({selected:!0})[0].length,a=i.localized.l10n.import_checked;l||(a=i.localized.l10n.import_all,l=r.rows()[0].length),a=a.replace("%d",l),e(i.fields.selector.finalize_button).html(a)}},i.fields.events.cancel_edit=function(e){e.preventDefault();var t=window.location.href;t=(t=t.replace("tab=edit","tab=scheduled")).replace(/id=\d+/,""),window.location.href=t},i.fields.events.verify_schedule_delete=function(){return confirm(i.localized.l10n.verify_schedule_delete)},i.fields.events.toggle_view_filters=function(t){t.preventDefault();var r=e(this);r.toggleClass("tribe-active"),r.is(".tribe-active")?r.html(i.localized.l10n.hide_filters):r.html(i.localized.l10n.view_filters)},i.fields.date_helper=function(){var t;if((t=e(this)).hasClass("tribe-datepicker")){var i=t.val();if(""!==i&&null!==i){var r=t.attr("id").match("tribe-ea-field-(.*)_start")[1];""!==r&&null!==r&&jQuery("#tribe-date-helper-date-"+r).html(i)}}},i.fields.maybeLimitUrlStartDate=function(){"url"===i.fields.origin.val()&&("schedule"!==i.fields.importType.val()?i.fields.urlImport.startDate.data("datepicker-min-date",null):i.fields.urlImport.startDate.data("datepicker-min-date","today"))},e(i.fields.init)}(jQuery,_,window.tribe_aggregator),window.tec=window.tec||{},window.tec.events=window.tec.events||{},window.tec.events.aggregatorFields={};