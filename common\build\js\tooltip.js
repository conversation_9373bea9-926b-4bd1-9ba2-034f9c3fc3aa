window.tribe=window.tribe||{},window.tribe.tooltip=window.tribe.tooltip||{},function(t,o){"use strict";const e=t(document);o.selectors={tooltip:".tribe-tooltip",active:"active"},o.setup=function(){e.on("click",o.selectors.tooltip,o.onClick),e.on("click",(function(e){e.target.closest(o.selectors.tooltip)||t(o.selectors.tooltip).each((function(){t(this).removeClass(o.selectors.active).attr("aria-expanded",!1)}))}))},o.onClick=function(){const e=t(this).closest(o.selectors.tooltip),i=!e.hasClass(o.selectors.active);t(o.selectors.tooltip).each((function(){t(this).removeClass(o.selectors.active).attr("aria-expanded",!1)})),i&&t(e).addClass(o.selectors.active).attr("aria-expanded",!0)},t(o.setup)}(jQ<PERSON>y,window.tribe.tooltip),window.tec=window.tec||{},window.tec.common=window.tec.common||{},window.tec.common.tooltip={};