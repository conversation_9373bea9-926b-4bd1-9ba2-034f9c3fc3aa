(()=>{var e=function(){const t=/d{1,4}|m{1,4}|yy(?:yy)?|([HhMsTt])\1?|[LloSZ]|"[^"]*"|'[^']*'/g,n=/\b(?:[PMCEA][SDP]T|(?:Pacific|Mountain|Central|Eastern|Atlantic) (?:Standard|Daylight|Prevailing) Time|(?:GMT|UTC)(?:[-+]\d{4})?)\b/g,i=/[^-+\dA-Z]/g,d=function(e,t){for(e=String(e),t=t||2;e.length<t;)e="0"+e;return e};return function(a,o,s){const r=e;if(1!=arguments.length||"[object String]"!=Object.prototype.toString.call(a)||/\d/.test(a)||(o=a,a=void 0),"string"==typeof a&&(a=a.replace(/-/g,"/")),a=a?new Date(a):new Date,isNaN(a))return;"UTC:"==(o=String(r.masks[o]||o||r.masks.default)).slice(0,4)&&(o=o.slice(4),s=!0);const l=s?"getUTC":"get",c=a[l+"Date"](),p=a[l+"Day"](),y=a[l+"Month"](),m=a[l+"FullYear"](),u=a[l+"Hours"](),f=a[l+"Minutes"](),h=a[l+"Seconds"](),v=a[l+"Milliseconds"](),g=s?0:a.getTimezoneOffset(),b={d:c,dd:d(c),ddd:r.i18n.dayNames[p],dddd:r.i18n.dayNames[p+7],m:y+1,mm:d(y+1),mmm:r.i18n.monthNames[y],mmmm:r.i18n.monthNames[y+12],yy:String(m).slice(2),yyyy:m,h:u%12||12,hh:d(u%12||12),H:u,HH:d(u),M:f,MM:d(f),s:h,ss:d(h),l:d(v,3),L:d(v>99?Math.round(v/10):v),t:u<12?"a":"p",tt:u<12?"am":"pm",T:u<12?"A":"P",TT:u<12?"AM":"PM",Z:s?"UTC":(String(a).match(n)||[""]).pop().replace(i,""),o:(g>0?"-":"+")+d(100*Math.floor(Math.abs(g)/60)+Math.abs(g)%60,4),S:["th","st","nd","rd"][c%10>3?0:(c%100-c%10!=10)*c%10]};return o.replace(t,(function(e){return e in b?b[e]:e.slice(1,e.length-1)}))}}();e.masks={default:"ddd mmm dd yyyy HH:MM:ss",tribeQuery:"yyyy-mm-dd",tribeMonthQuery:"yyyy-mm",0:"yyyy-mm-dd",1:"m/d/yyyy",2:"mm/dd/yyyy",3:"d/m/yyyy",4:"dd/mm/yyyy",5:"m-d-yyyy",6:"mm-dd-yyyy",7:"d-m-yyyy",8:"dd-mm-yyyy",9:"yyyy.mm.dd",10:"mm.dd.yyyy",11:"dd.mm.yyyy",m0:"yyyy-mm",m1:"m/yyyy",m2:"mm/yyyy",m3:"m/yyyy",m4:"mm/yyyy",m5:"m-yyyy",m6:"mm-yyyy",m7:"m-yyyy",m8:"mm-yyyy"},e.i18n={dayNames:["Sun","Mon","Tue","Wed","Thu","Fri","Sat","Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],monthNames:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec","January","February","March","April","May","June","July","August","September","October","November","December"]},Date.prototype.format=function(t,n){return e(this,t,n)};let t={};jQuery((function(n){n(".bumpdown-trigger").bumpdown();let i=n("[data-datepicker_format]"),d=(n(".tribe-field-dropdown_select2 select"),n(document.getElementById("view-calendar-link-div")).html()),a=(n('select[name="tribeEventsTemplate"]'),n(document.getElementById("tribe-event-datepickers"))),o=n("body").is(".tribe_community_edit"),s=0;const r={main:["yy-mm-dd","m/d/yy","mm/dd/yy","d/m/yy","dd/mm/yy","m-d-yy","mm-dd-yy","d-m-yy","dd-mm-yy","yy.mm.dd","mm.dd.yy","dd.mm.yy"],month:["yy-mm","m/yy","mm/yy","m/yy","mm/yy","m-yy","mm-yy","m-yy","mm-yy"]};function l(){const e=n(window).width();return e<800?1:e<=1100?2:3}n('[data-wp-lists="list:tribe_events_cat"]').each((function(){const e=n(this),t=e.find(":checkbox:checked").first();if(!t.length)return;const i=e.find(":checkbox").position().top,d=t.position().top;e.closest(".tabs-panel").scrollTop(d-i+5)})),n(d).insertAfter(".edit-php.post-type-tribe_events #wpbody-content .wrap h2:eq(0) a");const c=function(e){const t=n(document.getElementById("tmpl-tribe-select-"+e)).length?wp.template("tribe-select-"+e):null,i=n(document.getElementById("tmpl-tribe-create-"+e)).length?wp.template("tribe-create-"+e):null,d=n(document.getElementById("event_"+e)),a=d.find(".saved-linked-post");function o(e,t,i){if(void 0===window["tribe_sticky_"+e+"_fields"]||!Array.isArray(window["tribe_sticky_"+e+"_fields"]))return;const d=n(i),a=d.filter("tr.linked-post."+t);if(0===a.length)return;const o=window["tribe_sticky_"+e+"_fields"].shift();let s=!1;if("object"==typeof o)for(const e in o){const n=d.find('input[name="'+t+"["+e+'][]"]');n.length&&!_.isEmpty(o[e])&&(n.val(o[e]),s=!0)}s&&a.show()}d.on("click",".tribe-add-post",(function(a){a.preventDefault();let o=n({}),s=n({});if(t&&(o=n(t({}))),o.find(".nosaved").length){const t=o.find("label");t.text(t.data("l10n-create-"+e)),o.find(".nosaved").remove()}i&&(s=n(i({}))),d.find("tfoot:first").before(s),s.prepend(o),d.find("tbody").length>1?d.find(".move-linked-post-group").show():d.find(".move-linked-post-group").hide(),s.find(".tribe-dropdown").tribe_dropdowns(),o.find("select.linked-post-dropdown").trigger("change"),d.find("tbody").length>1?(d.find(".tribe-delete-this").show(),d.find(".move-linked-post-group").show()):(d.find(".tribe-delete-this").hide(),d.find(".move-linked-post-group").hide()),window.wp.hooks.doAction("tec.events.admin.linked_posts.add_post",e,d,s,o)})),d.on("change",".linked-post-dropdown",p),a.each((function(){const e=n(this),t=e.closest("tbody");let d;d=i?n(i({})).find("tr"):t.find("tr").slice(2);const a=e.find(".linked-post-dropdown");if(a.length){const e=a.val();0!==parseInt(e,10)&&d.not(".remain-visible").hide()}else if(e.find(".nosaved").length){const t=e.find("label");t.text(t.data("l10n-create-"+s)),e.find(".nosaved").remove()}for(var s in tribe_events_linked_posts.post_types)tribe_events_linked_posts.post_types.hasOwnProperty(s)&&o(s,tribe_events_linked_posts.post_types[s],d);d.find(".tribe-dropdown").tribe_dropdowns(),t.append(d)})),n(document).on("click",`#event_${e} .tribe-delete-this`,(function(e){e.preventDefault();const t=n(this).closest("tbody");t.parents(".tribe-section").removeClass("tribe-is-creating-linked-post"),t.fadeOut(500,(function(){n(this).remove(),t.find("select.linked-post-dropdown").trigger("change"),d.find("tbody").length>1?(d.find(".tribe-delete-this").show(),d.find(".move-linked-post-group").show()):(d.find(".tribe-delete-this").hide(),d.find(".move-linked-post-group").hide()),y(d)}))}));let s="> tbody";n("body").hasClass("wp-admin")||(s="table "+s),d.sortable({items:s,handle:".move-linked-post-group",containment:"parent",axis:"y",delay:100}),d.find("tbody").length>1?(d.find(".tribe-delete-this").show(),d.find(".move-linked-post-group").show()):(d.find(".tribe-delete-this").hide(),d.find(".move-linked-post-group").hide()),d.find("select.linked-post-dropdown").trigger("change")};var p=function(e){const t=n(this),i=t.data("postType"),d=t.parents(`#event_${i}`).eq(0),a=d.find("tbody"),o=(a.length,t.closest("tbody")),s=(a.index(o),o.find(".edit-linked-post-link a")),r=t.val(),l=t.find(":selected");let c="",p=!1;l.val()===r&&(c=l.data("editLink"),p=!!l.data("existingPost")),s.hide(),d.find("tfoot .tribe-add-post").show(),!p&&"-1"!==r&&r?(o.find(".linked-post-name").val(r).parents(".linked-post").eq(0).attr("data-hidden",!0),t.val("-1"),o.find(".linked-post").not("[data-hidden]").show().find(".tribe-dropdown"),o.parents(".tribe-section").addClass("tribe-is-creating-linked-post")):(o.find(".linked-post").hide().find("input, select").val(""),o.parents(".tribe-section").removeClass("tribe-is-creating-linked-post"),_.isEmpty(c)||s.attr("href",c).show()),y(d)};function y(e){const t=e.find("tbody"),n=t.length,i=e.find("tbody").last().find("select"),d=i.val(),a=i.find(":selected"),o=a.val(),s=i.closest("tbody"),r=t.index(s)+1;let l=!1;o===d&&(l=!!a.data("existingPost")),l||d&&"-1"!==d||r!==n?e.find("tfoot .tribe-add-post").show():e.find("tfoot .tribe-add-post").hide()}if(n(".hide-if-js").hide(),void 0!==tribe_l10n_datatables.datepicker){const d=864e5;let p="yy-mm-dd";i.length&&i.attr("data-datepicker_format").length>=1&&(s=i.attr("data-datepicker_format"),p=r.main[s]);let y=0;a.length&&(y=a.data("startofweek"));const m=n(document.getElementById("EventStartDate")),u=n(document.getElementById("EventEndDate")),f=n(document.getElementById("tribe_events_event_details"));t={dateFormat:p,showAnim:"fadeIn",changeMonth:!0,changeYear:!0,numberOfMonths:l(),showButtonPanel:!1,beforeShow(e,t){t.input.datepicker("option","numberOfMonths",l()),t.input.data("prevDate",t.input.datepicker("getDate")),void 0!==t.input.data("datepicker-min-date")&&t.input.datepicker("option","minDate",t.input.data("datepicker-min-date")),void 0!==t.input.data("datepicker-max-date")&&t.input.datepicker("option","maxDate",t.input.data("datepicker-max-date")),$dpDiv=n(t.dpDiv),$dpDiv.addClass("tribe-ui-datepicker"),f.trigger("tribe.ui-datepicker-div-beforeshow",[t]),$dpDiv.attrchange({trackValues:!0,callback(e){"string"==typeof e.newValue&&(e.newValue.indexOf("display: none")>=0||e.newValue.indexOf("display:none")>=0)&&($dpDiv.removeClass("tribe-ui-datepicker"),f.trigger("tribe.ui-datepicker-div-closed",[t]))}})},onSelect(e,t){const i=n(this).data("datepicker"),a=n.datepicker.parseDate(i.settings.dateFormat||n.datepicker._defaults.dateFormat,e,i.settings);if("EventStartDate"===this.id){const e=n(document.getElementById("EventStartDate")).data("prevDate"),t=null==e?0:function(e,t){const n=Date.UTC(e.getFullYear(),e.getMonth(),e.getDate()),i=Date.UTC(t.getFullYear(),t.getMonth(),t.getDate());return Math.floor((i-n)/d)}(e,u.datepicker("getDate")),i=new Date(a.setDate(a.getDate()+t));u.datepicker("option","minDate",m.datepicker("getDate")).datepicker("setDate",i).datepicker_format}n(this).trigger("change"),n(this).trigger("blur")}},n.extend(t,tribe_l10n_datatables.datepicker),window.tribe_datepicker_opts=t,n(".tribe-datepicker").datepicker(t);const h=n('select[name="EventStartMonth"], select[name="EventEndMonth"]'),v=n('select[name="EventStartMonth"]'),g=n('select[name="EventEndMonth"]');let b;if(o){const t={start:a.find("#EventStartDate"),end:a.next("tr").find("#EventEndDate")};n.each(t,(function(t,i){const d=n(i);""!==d.val()&&d.val(e(d.val(),s))}))}const w=[29,31,28,31,30,31,30,31,31,30,31,30,31],k=[n(document.getElementById("28StartDays")),n(document.getElementById("29StartDays")),n(document.getElementById("30StartDays")),n(document.getElementById("31StartDays"))],_=[n(document.getElementById("28EndDays")),n(document.getElementById("29EndDays")),n(document.getElementById("30EndDays")),n(document.getElementById("31EndDays"))];h.on("change",(function(){const e=n(this);let t=e.attr("name");t="EventStartMonth"==t?"Start":"End";let i=e.attr("value");"0"==i.charAt(0)&&(i=i.replace("0",""));const d=n('select[name="Event'+t+'Year"]').attr("value")%4;2==i&&0==d&&(i=0);const a=n('select[name="Event'+t+'Day"]');n(".event"+t+"DateField").remove(),"Start"==t?(b=k[w[i]-28],b.val(a.val()),v.after(b)):(b=_[w[i]-28],b.val(a.val()),g.after(b))})),h.trigger("change"),n('select[name="EventStartYear"]').on("change",(function(){v.trigger("change")})),n('select[name="EventEndYear"]').on("change",(function(){g.trigger("change")}));for(const e in tribe_events_linked_posts.post_types)tribe_events_linked_posts.post_types.hasOwnProperty(e)&&c(e)}window.wp.hooks.addAction("tec.events.admin.linked_posts.add_post","tec",((e,t,n)=>{"tribe_venue"===e&&n.find("#EventCountry").trigger("change")})),n(document).on("change",'[id="EventCountry"]',(function(){const e=n(this).parents("tbody").eq(0),t=e.find("#StateProvinceSelect"),i=t.next(".select2-container"),d=e.find("#StateProvinceText"),a=n(this).val();"US"==a||"United States"==a?(d.hide(),t.hide(),i.show()):(d.show(),t.hide(),i.hide())})).find("#EventCountry").trigger("change");const m={$container:n(document.getElementById("overwrite_coordinates"))};m.$lat=m.$container.find("#VenueLatitude"),m.$lng=m.$container.find("#VenueLongitude"),m.$fields=n("").add(m.$lat).add(m.$lng),m.$toggle=m.$container.find("#VenueOverwriteCoords").on("change",(function(e){m.$toggle.is(":checked")?m.$fields.prop("disabled",!1).removeClass("hidden"):m.$fields.prop("disabled",!0).addClass("hidden")})),m.$toggle.trigger("change"),n("#EventInfo input, #EventInfo select").on("change",(function(){n(".rec-error").hide()})),n('.wp-admin.events-cal #post #publishing-action input[type="submit"]').on("click",(function(){n(this).data("clicked",!0)})),n("body").hasClass("post-type-tribe_venue")&&n("#menu-posts-tribe_events, #menu-posts-tribe_events a.wp-has-submenu").addClass("wp-menu-open wp-has-current-submenu wp-has-submenu").removeClass("wp-not-current-submenu").find("li a[href='edit.php?post_type=tribe_venue']").parent().addClass("current"),n("body").hasClass("post-type-tribe_organizer")&&n("#menu-posts-tribe_events, #menu-posts-tribe_events a.wp-has-submenu").addClass("wp-menu-open wp-has-current-submenu wp-has-submenu").removeClass("wp-not-current-submenu").find("li a[href='edit.php?post_type=tribe_organizer']").parent().addClass("current");const u=n(document.getElementById("tribe-field-tribeEnableViews"));if(u.length){const e=n('select[name="viewOption"]'),t=n('select[name="mobile_default_view"]'),i=u.find("input:checkbox"),d=n("#tribe-field-tribeEnableViews .tribe-field-wrap p.description"),a={};e.find("option").each((function(){const e=n(this);a[e.attr("value")]=e.text()})),t.find("option").each((function(){const e=n(this);a[e.attr("value")]=e.text()})),u.on("change","input:checkbox",(function(){const a=n(this);n('[name="tribeEnableViews[]"]:checked').length<1?(a.attr("checked",!0),d.css("color","red")):d.removeAttr("style"),function(d){const a=e.find("option:selected").first().val(),o=t.find("option:selected").first().val();e.find("option").remove(),t.find("option").remove(),i.each((function(){const i=n(this);if(i.is(":checked")){const n=i.val(),d=n.substr(0,1).toUpperCase()+n.substr(1);e.append('<option value="'+n+'">'+d+"</option>"),t.append('<option value="'+n+'">'+d+"</option>")}}));const s=e.find("option[value='"+a+"']"),r=t.find("option[value='"+o+"']");if(s.val()==d.val())s.attr("selected","selected");else{var l=u.find("checkbox:checked").first().val();e.find("option").find("option[value='"+l+"']").attr("selected","selected")}r.val()==d.val()?r.attr("selected","selected"):(l=u.find("checkbox:checked").first().val(),t.find("option").find("option[value='"+l+"']").attr("selected","selected")),e.select2("destroy").select2({width:"auto"}),t.select2("destroy").select2({width:"auto"})}(a)}))}n("#tribe-community-events.form form").on("submit",(function(){const t={start:a.find("#EventStartDate"),end:a.next("tr").find("#EventEndDate")};t.start.val(e(t.start.datepicker("getDate"),"tribeQuery")),t.end.val(e(t.end.datepicker("getDate"),"tribeQuery")),a.parent().find(".tribe-no-end-date-update").each((function(){$el=n(this),$el.is(":visible")&&$el.val(e($el.datepicker("getDate"),"tribeQuery"))}))}))})),window.tec=window.tec||{},window.tec.events=window.tec.events||{},window.tec.events.eventsAdmin={}})();