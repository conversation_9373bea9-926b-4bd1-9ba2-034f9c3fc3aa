window.tribe_logger_admin=window.tribe_logger_admin||{},window.tribe_logger_data=window.tribe_logger_data||{},function(e){let n=!1,t="",o="",i=!1;const r=e("#tribe-log-controls"),a=r.find("select"),l=r.find(".working"),d=e("#tribe-log-viewer"),c=e("a.download_log");function g(e){f(),Array.isArray(e.data.entries)&&(d.html(function(e){let n="<table>";for(const t in e){n+="<tr>";for(const o in e[t])n+="<td>"+e[t][o]+"</td>";n+="</tr>"}return n+"</table>"}(e.data.entries)),s())}function s(){if(1>c.length)return;let e=c.attr("href");const n=encodeURI(m()),t=e.match(/&log=([a-z0-9\-]+)/i);e=Array.isArray(t)&&2===t.length?e.replace(t[0],"&log="+n):e.indexOf("?")?e+"&log="+n:e+"?log="+n,c.attr("href",e)}function w(){f()}function f(){n=!1,a.prop("disabled",!1),l.addClass("hidden")}function m(){return e("#log-selector").find(":selected").attr("name")}function u(){return e("#log-engine").find(":selected").attr("name")}t=m(),o=u(),s(),a.on("change",(function(){n||(function(){const e=m(),n=u();e!==t||n!==o?(i=!0,t=e,o=n):i=!1}(),n=!0,a.prop("disabled",!0),l.removeClass("hidden"),function(){const n={action:"tribe_logging_controls",check:tribe_logger_data.check,"log-level":e("#log-level").find(":selected").attr("name"),"log-engine":e("#log-engine").find(":selected").attr("name")};i&&(n["log-view"]=t),e.ajax(ajaxurl,{method:"POST",success:g,error:w,dataType:"json",data:n})}())}))}(jQuery,window.tribe_logger_admin),window.tec=window.tec||{},window.tec.common=window.tec.common||{},window.tec.common.adminLogControls={};