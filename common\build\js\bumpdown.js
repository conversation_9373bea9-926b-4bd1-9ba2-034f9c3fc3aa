!function(t,e){"use strict";t((function(){t(".tribe-bumpdown-trigger").bumpdown()})),t.fn.bumpdown=function(){var n=t(document),o={ID:"tribe-bumpdown-",data_trigger:t=>'[data-trigger="'+t+'"]',bumpdown:".tribe-bumpdown",content:".tribe-bumpdown-content",trigger:".tribe-bumpdown-trigger",hover_trigger:".tribe-bumpdown-trigger:not(.tribe-bumpdown-nohover)",close:".tribe-bumpdown-close",permanent:".tribe-bumpdown-permanent",active:".tribe-bumpdown-active"},r={open(e){const n=e.data("bumpdown"),i=n.$trigger.data("width-rule");if(e.is(":visible"))return;n.$trigger.addClass(o.active.replace(".",""));const d=e.find(o.content);if("string"==typeof i&&"all-triggers"===i){const e=600;let n=0;t(o.trigger).each((function(){const e=t(this);if(!e.data("width-rule"))return;const o=e.position();o.left>n&&(n=o.left)})),n&&(n=n>e?n:e,d.css("max-width",n+"px"))}d.prepend('<a class="tribe-bumpdown-close" title="Close"><i class="dashicons dashicons-no"></i></a>'),d.prepend('<span class="tribe-bumpdown-arrow"></span>'),r.arrow(e),e.data("preventClose",!0),e.slideDown("fast",(function(){e.data("preventClose",!1)}))},close(e){const n=e.data("bumpdown");e.is(":visible")&&!e.data("preventClose")&&(t(this).removeData("is_hoverintent_queued"),e.find(".tribe-bumpdown-close, .tribe-bumpdown-arrow").remove(),e.not(".tribe-bumpdown-trigger").slideUp("fast"),n.$trigger.removeClass(o.active.replace(".","")))},arrow(t){let e,n=t.data("bumpdown");e=Math.ceil(n.$trigger.position().left-("block"===n.type?n.$parent.offset().left:0)),n.$bumpdown.find(".tribe-bumpdown-arrow").css("left",e)}};return t(window).on({"resize.bumpdown"(){n.find(o.active).each((function(){r.arrow(t(this))}))}}),"function"==typeof t.fn.hoverIntent&&n.hoverIntent({over(){const e=t(this).data("bumpdown");e.$trigger.data("is_hoverintent_queued",!1),e.$bumpdown.trigger("open.bumpdown")},out(){},selector:o.hover_trigger,interval:200}),n.on({mouseenter(){void 0===t(this).data("is_hoverintent_queued")&&t(this).data("is_hoverintent_queued",!0)},click(e){const n=t(this).data("bumpdown");if(e.preventDefault(),e.stopPropagation(),n.$bumpdown.is(":visible")){if(n.$trigger.data("is_hoverintent_queued"))return n.$trigger.data("is_hoverintent_queued",!1);n.$bumpdown.trigger("close.bumpdown")}else n.$bumpdown.trigger("open.bumpdown")},"open.bumpdown"(){r.open(t(this))},"close.bumpdown"(){r.close(t(this))}},o.trigger).on({click(e){const n=t(this).parents(o.bumpdown).first().data("bumpdown");e.preventDefault(),e.stopPropagation(),void 0!==n&&void 0!==n.$bumpdown&&n.$bumpdown.trigger("close.bumpdown")}},o.close).on("click",(function(e){const n=t(e.target);n.is(o.bumpdown)||0!==n.parents(o.bumpdown).length||t(o.trigger).not(o.permanent).trigger("close.bumpdown")})).on({"open.bumpdown"(){r.open(t(this))},"close.bumpdown"(){r.close(t(this))}},o.bumpdown),this.each((function(){const n={$trigger:t(this),$parent:null,$bumpdown:null,ID:null,html:null,type:"block",is_permanent:!1};if(n.ID=n.$trigger.attr("id"),n.ID||(n.ID=e.uniqueId(o.ID),n.$trigger.attr("id",n.ID)),n.html=n.$trigger.attr("data-bumpdown"),n.html='<div class="tribe-bumpdown-content">'+n.html+"</div>",n.class=n.$trigger.attr("data-bumpdown-class"),n.is_permanent=n.$trigger.is(o.permanent),n.$parent=n.$trigger.parents().filter((function(){return-1<t.inArray(t(this).css("display"),["block","table","table-cell","table-row"])})).first(),n.html)if(n.type=n.$parent.is("td, tr, td, table")?"table":"block","table"===n.type){n.$bumpdown=t("<td>").attr({colspan:2}).addClass("tribe-bumpdown-cell").html(n.html);const e=n.class?"tribe-bumpdown-row "+n.class:"tribe-bumpdown-row",o=t("<tr>").append(n.$bumpdown).addClass(e);n.$parent=n.$trigger.parents("tr").first(),n.$parent.after(o)}else n.$bumpdown=t("<div>").addClass("tribe-bumpdown-block").html(n.html),n.$trigger.after(n.$bumpdown);else n.$bumpdown=t(o.data_trigger(n.ID)),n.type="block";if(n.$trigger.data("bumpdown",n).addClass(o.trigger.replace(".","")),n.$bumpdown.data("bumpdown",n).addClass(o.bumpdown.replace(".","")),n.$trigger.data("depends")){const e=n.$trigger.data("depends");t(document).on("change",e,(function(){r.close(n.$bumpdown)}))}}))}}(jQuery,window.underscore||window._),window.tec=window.tec||{},window.tec.common=window.tec.common||{},window.tec.common.bumpdown={};